#!/bin/bash

# Gluetun FastestVPN Installation Module
# This module handles FastestVPN installation functionality

# Install Gluetun with FastestVPN configuration
install_gluetun_fastestvpn() {
    echo "Installing Gluetun with FastestVPN configuration..."
    
    # Install Docker if not already installed
    install_docker

    # Check for existing containers
    if ! check_existing_containers; then
        return 0
    fi

    # Check if jq is installed for JSON parsing
    if ! command -v jq &> /dev/null; then
        echo "Installing jq for JSON parsing..."
        DEBIAN_FRONTEND=noninteractive sudo apt-get update >/dev/null 2>&1
        DEBIAN_FRONTEND=noninteractive sudo apt-get install -y jq >/dev/null 2>&1
    fi

    # Get FastestVPN server selection
    local server_info
    if ! server_info=$(select_fastestvpn_server); then
        return 1
    fi
    
    # Parse server information
    IFS='|' read -r selected_country selected_city selected_server_ip <<< "$server_info"
    
    echo "Selected server: $selected_country:$selected_city ($selected_server_ip)"

    # FastestVPN WireGuard configuration (predefined)
    local WIREGUARD_PRIVATE_KEY="yOLPOnN+pzly6VygYbjUnecHBc4jLcJRQcZSv5eWZmk="
    local WIREGUARD_ADDRESSES="*************/32"
    local WIREGUARD_DNS="********"
    local WIREGUARD_PUBLIC_KEY="658QxufMbjOTmB61Z7f+c7Rjg7oqWLnepTalqBERjF0="
    local WIREGUARD_ENDPOINT_PORT="51820"
    local WIREGUARD_ENDPOINT_IP="$selected_server_ip"

    # Get container name from user
    local default_name=$(get_next_gluetun_number)
    local container_name
    if ! container_name=$(get_container_name "$default_name"); then
        return 1
    fi

    # Get port configuration
    local port_config
    port_config=$(get_port_configuration "$container_name")
    local ports=$(echo "$port_config" | cut -d'|' -f1)
    local port_mappings=$(echo "$port_config" | cut -d'|' -f2)

    # Display configuration summary
    echo "Using FastestVPN configuration:"
    echo "Country: $selected_country"
    echo "City: $selected_city"
    echo "Endpoint: ${WIREGUARD_ENDPOINT_IP}:${WIREGUARD_ENDPOINT_PORT}"
    echo "Address: ${WIREGUARD_ADDRESSES}"
    echo "DNS: ${WIREGUARD_DNS}"
    echo "Container: $container_name"
    echo "Ports: $ports"
    echo
    
    read -p "Do you want to proceed with the installation? [Y/n]: " confirm
    if [[ -n "$confirm" && ! "${confirm:0:1}" =~ [Yy] ]]; then
        echo "Installation cancelled."
        return 0
    fi

    # Create and start the Gluetun container with FastestVPN configuration
    echo "Creating Gluetun container with FastestVPN configuration..."
    if create_fastestvpn_container "$container_name" "$port_mappings" \
        "$WIREGUARD_PRIVATE_KEY" "$WIREGUARD_ADDRESSES" "$WIREGUARD_DNS" \
        "$WIREGUARD_PUBLIC_KEY" "$WIREGUARD_ENDPOINT_IP" "$WIREGUARD_ENDPOINT_PORT"; then
        
        echo "✓ Gluetun container '$container_name' created successfully!"
        echo "✓ FastestVPN server: $selected_country:$selected_city ($selected_server_ip)"
        echo "✓ Exposed ports: $ports"
        echo
        echo "Container is starting up. You can check its status with:"
        echo "docker logs $container_name"
        return 0
    else
        echo "✗ Failed to create Gluetun container"
        return 1
    fi
}

# Select FastestVPN server from the server list
select_fastestvpn_server() {
    # Path to the FastestVPN server list
    local server_list_file="$(dirname "${BASH_SOURCE[0]}")/fastestvpn_server_list.json"
    
    if [ ! -f "$server_list_file" ]; then
        echo "Error: FastestVPN server list file not found at $server_list_file"
        echo "Current directory: $(pwd)"
        echo "Script location: ${BASH_SOURCE[0]}"
        return 1
    fi

    echo "Found server list file at: $server_list_file"
    echo "Checking file contents..."
    if ! jq . "$server_list_file" >/dev/null 2>&1; then
        echo "Error: Invalid JSON in server list file"
        return 1
    fi

    echo "Processing server list..."
    
    # Read the server list with error handling
    local json_output
    json_output=$(jq -r '.[] | .country + ":" + .city + "|" + .ip_address + "|" + .domain' "$server_list_file" 2>&1)
    if [ $? -ne 0 ]; then
        echo "Error processing JSON file: $json_output"
        return 1
    fi

    local raw_servers=()
    while IFS= read -r line; do
        raw_servers+=("$line")
    done <<< "$json_output"

    if [ ${#raw_servers[@]} -eq 0 ]; then
        echo "Error: No servers found in the server list"
        return 1
    fi

    echo "Found ${#raw_servers[@]} servers"
    
    # Simple array to store servers
    local server_list=()
    
    # Process and store servers
    for server in "${raw_servers[@]}"; do
        if [ -n "$server" ]; then
            server_list+=("$server")
        fi
    done
    
    # Check if we have servers to display
    if [ ${#server_list[@]} -eq 0 ]; then
        echo "Error: No valid servers found"
        return 1
    fi
    
    # Display servers in a simple format
    echo
    echo "Available FastestVPN Servers:"
    echo "----------------------------"
    local counter=1
    
    for server in "${server_list[@]}"; do
        IFS='|' read -r location ip domain <<< "$server"
        echo "$counter) $location - $ip"
        ((counter++))
    done
    echo "----------------------------"
    echo
    
    # Get user selection
    while true; do
        read -p "Select a server (1-${#server_list[@]}): " server_choice
        
        if [[ "$server_choice" =~ ^[0-9]+$ ]] && [ "$server_choice" -ge 1 ] && [ "$server_choice" -le "${#server_list[@]}" ]; then
            selected_server="${server_list[$((server_choice-1))]}"
            IFS='|' read -r selected_country_city selected_server_ip selected_domain <<< "$selected_server"
            IFS=':' read -r selected_country selected_city <<< "$selected_country_city"
            echo "Selected: $selected_country_city ($selected_server_ip)"
            
            # Return the server information
            echo "${selected_country}|${selected_city}|${selected_server_ip}"
            return 0
        else
            echo "Invalid selection. Please enter a number between 1 and ${#server_list[@]}."
        fi
    done
}

# Create FastestVPN Gluetun container
create_fastestvpn_container() {
    local container_name="$1"
    local port_mappings="$2"
    local private_key="$3"
    local addresses="$4"
    local dns="$5"
    local public_key="$6"
    local endpoint_ip="$7"
    local endpoint_port="$8"
    
    docker run -d \
        --name="$container_name" \
        --cap-add=NET_ADMIN \
        --device /dev/net/tun:/dev/net/tun \
        -e VPN_SERVICE_PROVIDER=custom \
        -e VPN_TYPE=wireguard \
        -e WIREGUARD_PRIVATE_KEY="$private_key" \
        -e WIREGUARD_ADDRESSES="$addresses" \
        -e WIREGUARD_DNS="$dns" \
        -e WIREGUARD_PUBLIC_KEY="$public_key" \
        -e WIREGUARD_ENDPOINT_IP="$endpoint_ip" \
        -e WIREGUARD_ENDPOINT_PORT="$endpoint_port" \
        -e TZ=UTC \
        $port_mappings \
        --restart unless-stopped \
        fazee6/gluetun

    return $?
}
