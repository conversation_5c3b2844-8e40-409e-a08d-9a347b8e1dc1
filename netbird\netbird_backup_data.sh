#!/bin/bash

# Netbird data and volume backup functions

# Function to backup Docker volumes
backup_netbird_volumes() {
    local netbird_path=$(detect_netbird_installation)

    if [ -z "$netbird_path" ]; then
        echo "Netbird installation not found."
        return 1
    fi

    # Detect domain name for organized backup
    local domain=$(detect_netbird_domain)
    if [ -z "$domain" ]; then
        echo "Warning: Could not detect domain name. Using 'unknown-domain' as fallback."
        domain="unknown-domain"
    fi

    echo "Detected domain: $domain"

    local backup_dir="/opt/netbird_backups"
    local timestamp=$(date +"%Y%m%d_%H%M%S")
    local volumes_backup_file="netbird_volumes_backup_${timestamp}.tar.gz"
    local volumes_backup_path="$backup_dir/$volumes_backup_file"
    local rclone_dest="$RCLONE_REMOTE:/Backups/Netbird/$domain"

    echo "Backing up Netbird Docker volumes..."

    # Create backup directory
    mkdir -p "$backup_dir"

    cd "$netbird_path"

    # Get list of volumes used by Netbird - improved detection
    local volumes=""
    local temp_volumes_dir="$backup_dir/volumes_temp"
    mkdir -p "$temp_volumes_dir"

    # Stop all services for consistent backup
    echo "Stopping all Netbird services for consistent volume backup..."
    docker compose stop

    # Get volumes from docker compose config
    volumes=$(docker compose config --volumes 2>/dev/null | tr '\n' ' ')

    # Also get volumes from running containers (with netbird prefix)
    local netbird_volumes=$(docker volume ls --format "{{.Name}}" | grep -E "netbird.*netbird" | tr '\n' ' ')
    volumes="$volumes $netbird_volumes"

    # Remove duplicates and empty entries, prioritize prefixed volumes
    volumes=$(echo "$volumes" | tr ' ' '\n' | sort -u | grep -v '^$')

    # Filter out non-prefixed duplicates (prefer netbird_netbird_* over netbird_*)
    local filtered_volumes=""
    for vol in $volumes; do
        if [[ "$vol" =~ ^netbird_[^_]+$ ]]; then
            # Check if prefixed version exists
            local prefixed_vol="netbird_$vol"
            if echo "$volumes" | grep -q "^$prefixed_vol$"; then
                continue  # Skip non-prefixed if prefixed exists
            fi
        fi
        filtered_volumes="$filtered_volumes $vol"
    done

    volumes=$(echo "$filtered_volumes" | tr ' ' '\n' | sort -u | grep -v '^$' | tr '\n' ' ')

    echo "Found volumes to backup: $volumes"

    # Backup each volume
    echo "Backing up Docker volumes..."
    local volumes_backed_up=0

    for volume in $volumes; do
        volume=$(echo "$volume" | tr -d ' ')  # Remove any whitespace
        if [ -n "$volume" ] && docker volume inspect "$volume" >/dev/null 2>&1; then
            echo "Backing up volume: $volume"
            local volume_backup_dir="$temp_volumes_dir/$volume"
            mkdir -p "$volume_backup_dir"

            # Create a temporary container to access the volume
            if docker run --rm -v "$volume:/volume" -v "$volume_backup_dir:/backup" alpine:latest \
                sh -c "cd /volume && tar -czf /backup/volume_data.tar.gz . 2>/dev/null" 2>/dev/null; then
                echo "Successfully backed up volume: $volume"
                ((volumes_backed_up++))
            else
                echo "Warning: Failed to backup volume $volume"
            fi
        else
            echo "Volume $volume not found or inaccessible"
        fi
    done

    echo "Total volumes backed up: $volumes_backed_up"

    # Create archive of all volume backups
    if [ "$(ls -A "$temp_volumes_dir" 2>/dev/null)" ]; then
        echo "Creating volumes backup archive..."
        cd "$backup_dir"
        if tar -czf "$volumes_backup_file" -C "$temp_volumes_dir" .; then
            echo "Volumes backup created successfully."
            rm -rf "$temp_volumes_dir"

            # Upload to cloud storage (overwrite existing backup for this domain)
            echo "Uploading volumes backup to cloud storage..."
            echo "Destination: $rclone_dest"

            # Remove existing volumes backup for this domain first
            echo "Removing any existing volumes backup for domain: $domain"
            rclone delete "$rclone_dest" --include "netbird_volumes_backup_*.tar.gz" 2>/dev/null || true

            if rclone copy "$volumes_backup_path" "$rclone_dest" --progress; then
                echo "Volumes backup uploaded successfully to domain folder: $domain"
                rm -f "$volumes_backup_path"

                # Restart services
                echo "Restarting Netbird services..."
                cd "$netbird_path"
                docker compose up -d
                return 0
            else
                echo "Failed to upload volumes backup."
                rm -f "$volumes_backup_path"
                cd "$netbird_path"
                docker compose up -d
                return 1
            fi
        else
            echo "Failed to create volumes backup archive."
            rm -rf "$temp_volumes_dir"
            cd "$netbird_path"
            docker compose up -d
            return 1
        fi
    else
        echo "No volumes found to backup."
        rm -rf "$temp_volumes_dir"
        cd "$netbird_path"
        docker compose up -d
        return 0
    fi
}

# Function to backup Netbird data (databases and persistent data)
backup_netbird_data() {
    local netbird_path=$(detect_netbird_installation)

    if [ -z "$netbird_path" ]; then
        echo "Netbird installation not found."
        return 1
    fi

    # Detect domain name for organized backup
    local domain=$(detect_netbird_domain)
    if [ -z "$domain" ]; then
        echo "Warning: Could not detect domain name. Using 'unknown-domain' as fallback."
        domain="unknown-domain"
    fi

    echo "Detected domain: $domain"

    local backup_dir="/opt/netbird_backups"
    local timestamp=$(date +"%Y%m%d_%H%M%S")
    local data_backup_file="netbird_data_backup_${timestamp}.tar.gz"
    local data_backup_path="$backup_dir/$data_backup_file"
    local rclone_dest="$RCLONE_REMOTE:/Backups/Netbird/$domain"

    echo "Backing up Netbird application data..."

    # Check if rclone is installed
    if ! install_rclone; then
        echo "Failed to verify/install Rclone. Backup cannot proceed."
        return 1
    fi

    # Create backup directory
    mkdir -p "$backup_dir"

    # Check compression tools
    if ! check_compression_tools; then
        echo "Required compression tools are not available."
        return 1
    fi

    cd "$netbird_path"

    # Create comprehensive data backup
    local temp_data_dir="$backup_dir/netbird_data_temp"
    mkdir -p "$temp_data_dir"

    # Stop services for consistent backup
    echo "Stopping services for consistent data backup..."
    docker compose stop

    # Backup management data
    echo "Backing up management service data..."
    if docker compose cp -a management:/var/lib/netbird/ "$temp_data_dir/management_data/" 2>/dev/null; then
        echo "Management data backed up successfully."
    else
        echo "Warning: Could not backup management data (container may not exist yet)."
    fi

    # Backup Zitadel database - improved detection and backup
    echo "Backing up Zitadel database..."
    local db_container=""

    # Find database container - improved detection
    echo "Detecting database container..."

    # Method 1: Check running containers by service name
    if docker compose ps --format "{{.Service}}" 2>/dev/null | grep -q "^zdb$"; then
        db_container="zdb"
        echo "Found database service: zdb"
    elif docker compose ps --format "{{.Service}}" 2>/dev/null | grep -q "^postgres$"; then
        db_container="postgres"
        echo "Found database service: postgres"
    else
        # Method 2: Check all services in compose file
        if docker compose config --services 2>/dev/null | grep -q "^zdb$"; then
            db_container="zdb"
            echo "Found database service in config: zdb"
        elif docker compose config --services 2>/dev/null | grep -q "^postgres$"; then
            db_container="postgres"
            echo "Found database service in config: postgres"
        else
            # Method 3: Check by container name pattern
            if docker ps --format "{{.Names}}" | grep -q "zdb"; then
                db_container="zdb"
                echo "Found database container by name pattern: zdb"
            elif docker ps --format "{{.Names}}" | grep -q "postgres"; then
                db_container="postgres"
                echo "Found database container by name pattern: postgres"
            else
                echo "No database service found in compose configuration or running containers"
            fi
        fi
    fi

    if [ -n "$db_container" ]; then
        mkdir -p "$temp_data_dir/zitadel_db"
        echo "Found database container: $db_container"

        # Start only the database container for backup
        echo "Starting database container for backup..."
        docker compose up -d "$db_container"

        # Wait for database to be ready
        echo "Waiting for database to be ready..."
        sleep 15

        # Try multiple backup methods
        local backup_success=false

        # Method 1: pg_dumpall
        echo "Attempting database backup with pg_dumpall..."
        if docker compose exec -T "$db_container" pg_dumpall -U postgres > "$temp_data_dir/zitadel_db/zitadel_backup.sql" 2>/dev/null; then
            # Verify backup file is not empty
            if [ -s "$temp_data_dir/zitadel_db/zitadel_backup.sql" ]; then
                local backup_size=$(stat -c%s "$temp_data_dir/zitadel_db/zitadel_backup.sql" 2>/dev/null || echo "0")
                echo "Zitadel database backed up successfully with pg_dumpall ($backup_size bytes)."
                backup_success=true
            else
                echo "pg_dumpall produced empty backup file."
            fi
        else
            echo "pg_dumpall failed, trying alternative method..."

            # Method 2: pg_dump for specific databases
            echo "Attempting backup of individual databases..."
            if docker compose exec -T "$db_container" psql -U postgres -c "\l" 2>/dev/null | grep -q "zitadel"; then
                if docker compose exec -T "$db_container" pg_dump -U postgres zitadel > "$temp_data_dir/zitadel_db/zitadel_db_backup.sql" 2>/dev/null; then
                    echo "Zitadel database backed up successfully with pg_dump."
                    backup_success=true
                fi
            fi
        fi

        if ! $backup_success; then
            echo "Warning: Could not backup Zitadel database using SQL dump methods."
            echo "Attempting to backup database data directory..."

            # Method 3: Backup data directory
            if docker compose exec -T "$db_container" tar -czf /tmp/postgres_data.tar.gz /var/lib/postgresql/data 2>/dev/null; then
                docker compose cp "$db_container:/tmp/postgres_data.tar.gz" "$temp_data_dir/zitadel_db/" 2>/dev/null && {
                    echo "Database data directory backed up successfully."
                    backup_success=true
                }
            fi
        fi

        if ! $backup_success; then
            echo "Warning: All database backup methods failed."
        fi

        docker compose stop "$db_container"
    else
        echo "Warning: No database container found for backup."
    fi

    # Backup machine keys and certificates
    echo "Backing up certificates and keys..."
    if [ -d "machinekey" ]; then
        cp -r machinekey "$temp_data_dir/" 2>/dev/null || echo "Warning: Could not backup machine keys."
    fi

    # Backup any additional data directories
    for dir in "certs" "ssl" "data" "logs"; do
        if [ -d "$dir" ]; then
            cp -r "$dir" "$temp_data_dir/" 2>/dev/null
        fi
    done

    # Create tar archive of all data
    echo "Creating comprehensive data backup archive..."
    cd "$backup_dir"
    if tar -czf "$data_backup_file" -C "$temp_data_dir" .; then
        echo "Data backup created successfully."
        rm -rf "$temp_data_dir"

        # Upload to cloud storage (overwrite existing backup for this domain)
        echo "Uploading data backup to cloud storage..."
        echo "Destination: $rclone_dest"

        # Remove existing data backup for this domain first
        echo "Removing any existing data backup for domain: $domain"
        rclone delete "$rclone_dest" --include "netbird_data_backup_*.tar.gz" 2>/dev/null || true

        if rclone copy "$data_backup_path" "$rclone_dest" --progress; then
            echo "Data backup uploaded successfully to domain folder: $domain"
            rm -f "$data_backup_path"

            # Restart all services
            echo "Restarting Netbird services..."
            cd "$netbird_path"
            docker compose up -d
            return 0
        else
            echo "Failed to upload data backup."
            rm -f "$data_backup_path"
            cd "$netbird_path"
            docker compose up -d
            return 1
        fi
    else
        echo "Failed to create data backup archive."
        rm -rf "$temp_data_dir"
        cd "$netbird_path"
        docker compose up -d
        return 1
    fi
}

# Function to perform full Netbird backup (config + data + volumes)
backup_netbird_full() {
    echo "Starting COMPLETE Netbird backup..."
    echo "==================================="
    echo "This will backup:"
    echo "- Configuration files (docker-compose.yml, etc.)"
    echo "- Application data (management database, etc.)"
    echo "- Docker volumes (Zitadel DB, Caddy data, certificates)"
    echo "- Machine keys and certificates"
    echo ""

    local netbird_path=$(detect_netbird_installation)
    if [ -z "$netbird_path" ]; then
        echo "Netbird installation not found."
        return 1
    fi

    local success=true
    local backup_timestamp=$(date +"%Y%m%d_%H%M%S")

    echo "Backup timestamp: $backup_timestamp"
    echo ""

    # Backup configuration
    echo "Step 1/3: Backing up configuration files..."
    if ! backup_netbird_config; then
        echo "Configuration backup failed."
        success=false
    fi
    echo ""

    # Backup application data
    echo "Step 2/3: Backing up application data and databases..."
    if ! backup_netbird_data; then
        echo "Data backup failed."
        success=false
    fi
    echo ""

    # Backup Docker volumes
    echo "Step 3/3: Backing up Docker volumes..."
    if ! backup_netbird_volumes; then
        echo "Volumes backup failed."
        success=false
    fi
    echo ""

    if $success; then
        echo "========================================="
        echo "COMPLETE Netbird backup finished successfully!"
        echo "========================================="
        echo "Backup includes:"
        echo "✓ Configuration files"
        echo "✓ Management database"
        echo "✓ Zitadel identity provider database"
        echo "✓ Docker volumes (Caddy, certificates, etc.)"
        echo "✓ Machine keys and certificates"
        echo ""
        echo "This backup can be used to restore a fully working"
        echo "Netbird installation on any compatible server."
        return 0
    else
        echo "========================================="
        echo "COMPLETE Netbird backup finished with errors!"
        echo "========================================="
        echo "Some components may not have been backed up properly."
        echo "Check the output above for details."
        return 1
    fi
}
