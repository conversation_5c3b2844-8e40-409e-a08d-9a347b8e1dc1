#!/bin/bash

# Gluetun Core Utilities Module
# This module contains shared functions and utilities used across all Gluetun modules

# Get the next available Gluetun container number
get_next_gluetun_number() {
    local prefix="gluetun-"
    local i=1
    
    while true; do
        if ! docker ps -a --format '{{.Names}}' | grep -q "^${prefix}${i}$"; then
            echo "${prefix}${i}"
            return
        fi
        ((i++))
    done
}

# Get the port configuration for a Gluetun container
get_gluetun_port() {
    local container_name="$1"
    # Get port mapping from docker container
    local port_mapping=$(docker port "$container_name" 2>/dev/null | grep -E '[0-9]+->5800/tcp' | cut -d ':' -f2 | cut -d'-' -f1)
    
    if [ -n "$port_mapping" ]; then
        echo "$port_mapping"
    else
        # If container doesn't exist or no port mapping found, calculate next available port
        local prefix="gluetun-"
        local number=$(echo "$container_name" | sed "s/${prefix}//")
        if [[ "$number" =~ ^[0-9]+$ ]]; then
            echo $((5800 + number - 1))
        else
            echo "5800"
        fi
    fi
}

# Validate port numbers
validate_ports() {
    local ports=$1
    # Remove trailing spaces
    ports=$(echo "$ports" | sed 's/[[:space:]]*$//')
    
    # Check if input is empty
    if [ -z "$ports" ]; then
        return 1
    fi
    
    # Check each port
    for port in $ports; do
        # Check if port is a number and within valid range (1-65535)
        if ! [[ "$port" =~ ^[0-9]+$ ]] || [ "$port" -lt 1 ] || [ "$port" -gt 65535 ]; then
            return 1
        fi
    done
    return 0
}

# Validate container name format
validate_container_name() {
    local name="$1"
    if [[ $name =~ ^[a-zA-Z0-9_-]+$ ]]; then
        return 0
    else
        return 1
    fi
}

# Check if container name already exists
container_name_exists() {
    local name="$1"
    if docker ps -a --format '{{.Names}}' | grep -q "^${name}$"; then
        return 0
    else
        return 1
    fi
}

# Get container name from user with validation
get_container_name() {
    local default_name="$1"
    local container_name
    
    while true; do
        read -p "Enter container name [$default_name]: " container_name
        container_name=${container_name:-$default_name}
        
        # Check if the chosen name already exists
        if container_name_exists "$container_name"; then
            echo "Error: A container with name '$container_name' already exists."
            read -p "Do you want to try a different name? [Y/n]: " retry
            if [[ -z "$retry" || "${retry:0:1}" =~ [Yy] ]]; then
                continue
            else
                echo "Installation cancelled."
                return 1
            fi
        fi
        
        if validate_container_name "$container_name"; then
            echo "$container_name"
            return 0
        else
            echo "Error: Container name can only contain letters, numbers, underscores, and hyphens."
            echo "Please try again."
            echo
        fi
    done
}

# Get port configuration from user
get_port_configuration() {
    local container_name="$1"

    while true; do
        # Extract number from container name for default port calculation
        local number=$(echo "$container_name" | grep -o '[0-9]*$')
        # If no number found, default to 1
        if [ -z "$number" ]; then
            number=1
        fi
        local default_port=$((5800 + number - 1))

        echo "Enter port(s) to expose (1 or more ports separated by spaces)"
        echo "Example: 5800 8080 9090"
        echo "Press Enter for default port ($default_port)"
        read -p "> " user_ports

        # If empty, use default
        if [ -z "$user_ports" ]; then
            GLUETUN_PORTS="$default_port"
            GLUETUN_PORT_MAPPINGS="-p ${default_port}:${default_port}/tcp -p ${default_port}:${default_port}/udp"
            return 0
        fi

        # Validate ports
        if validate_ports "$user_ports"; then
            GLUETUN_PORTS=$user_ports
            GLUETUN_PORT_MAPPINGS=""
            for port in $GLUETUN_PORTS; do
                # Map both TCP and UDP for each port
                if [ -z "$GLUETUN_PORT_MAPPINGS" ]; then
                    GLUETUN_PORT_MAPPINGS="-p ${port}:${port}/tcp -p ${port}:${port}/udp"
                else
                    GLUETUN_PORT_MAPPINGS="${GLUETUN_PORT_MAPPINGS} -p ${port}:${port}/tcp -p ${port}:${port}/udp"
                fi
            done
            return 0
        else
            echo "Error: Invalid port number(s). Ports must be between 1 and 65535."
            echo "Please try again."
            echo
        fi
    done
}

# Check for existing Gluetun containers and ask user if they want to create another
check_existing_containers() {
    local containers=$(docker ps -a --filter ancestor=fazee6/gluetun --format '{{.Names}}')
    
    if [ -n "$containers" ]; then
        echo "Found the following Gluetun containers:"
        echo "$containers" | nl
        
        read -p "Do you want to create another Gluetun container? [Y/n]: " create_new
        if [[ -n "$create_new" && ! "${create_new:0:1}" =~ [Yy] ]]; then
            echo "Installation cancelled."
            return 1
        fi
    fi
    return 0
}

# Wait for container to initialize and check status
check_container_status() {
    local container_name="$1"
    local ports="$2"
    local dns_info="$3"
    
    # Wait for container to initialize
    echo "Waiting for container to initialize..."
    sleep 5

    # Simple status check after container creation
    if docker ps | grep -q "${container_name}"; then
        echo "Gluetun container '${container_name}' started successfully with ports:"
        echo "Web UI: ${ports}"
        # When multiple ports are specified, only show VNC port for the first one
        first_port=$(echo $ports | awk '{print $1}')
        echo "VNC: $((first_port+100))"
        if [ -n "$dns_info" ]; then
            echo "Using DNS server(s): ${dns_info}"
        fi
        return 0
    else
        echo "Error: Container failed to start"
        return 1
    fi
}

# Common Docker run parameters for Gluetun containers
get_common_docker_params() {
    echo "--cap-add=NET_ADMIN --device=/dev/net/tun --dns ******* --dns ******* --restart unless-stopped"
}

# Common environment variables for Gluetun containers
get_common_env_vars() {
    local ports="$1"
    local timezone=$(cat /etc/timezone 2>/dev/null || echo "UTC")
    echo "-e DOT=off -e DNS_KEEP_NAMESERVER=false -e FIREWALL_VPN_INPUT_PORTS=\"$(echo ${ports} | tr ' ' ',')\" -e HEALTH_TARGET_ADDRESS=******* -e HEALTH_VPN_DURATION_INITIAL=30s -e UPDATER_PERIOD=24h -e TZ=${timezone}"
}
