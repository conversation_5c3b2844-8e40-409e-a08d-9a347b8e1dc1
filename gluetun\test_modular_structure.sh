#!/bin/bash

# Test script to verify the modular Gluetun structure works correctly
# This script checks if all functions are available after sourcing the main file

echo "Testing Gluetun modular structure..."

# Source the main functions file
source "$(dirname "${BASH_SOURCE[0]}")/gluetun_functions.sh"

# List of functions that should be available
expected_functions=(
    "install_gluetun_pia"
    "install_gluetun_fastestvpn"
    "install_gluetun_custom"
    "remove_gluetun"
    "get_next_gluetun_number"
    "get_gluetun_port"
    "validate_ports"
    "validate_container_name"
    "get_container_name"
    "get_port_configuration"
    "check_existing_containers"
    "check_container_status"
    "select_fastestvpn_server"
    "get_custom_vpn_config"
    "select_gluetun_container"
    "handle_dependent_containers"
    "get_gluetun_status"
    "start_gluetun"
    "stop_gluetun"
    "restart_gluetun"
)

# Check if each function is available
missing_functions=()
available_functions=()

for func in "${expected_functions[@]}"; do
    if declare -f "$func" > /dev/null; then
        available_functions+=("$func")
        echo "✓ $func - Available"
    else
        missing_functions+=("$func")
        echo "✗ $func - Missing"
    fi
done

echo
echo "Summary:"
echo "Available functions: ${#available_functions[@]}"
echo "Missing functions: ${#missing_functions[@]}"

if [ ${#missing_functions[@]} -eq 0 ]; then
    echo "✓ All expected functions are available!"
    echo "✓ Modular structure is working correctly!"
    exit 0
else
    echo "✗ Some functions are missing:"
    for func in "${missing_functions[@]}"; do
        echo "  - $func"
    done
    exit 1
fi
