#!/bin/bash

# Gluetun Custom VPN Installation Module
# This module handles custom VPN configuration installation functionality

# Install Gluetun with custom configuration
install_gluetun_custom() {
    install_docker

    # Check for existing containers
    if ! check_existing_containers; then
        return 0
    fi

    # Get container name from user
    local default_name=$(get_next_gluetun_number)
    local container_name
    if ! container_name=$(get_container_name "$default_name"); then
        return 1
    fi

    # Get port configuration
    get_port_configuration "$container_name"
    local ports="$GLUETUN_PORTS"
    local port_mappings="$GLUETUN_PORT_MAPPINGS"

    # Get custom VPN configuration from user
    local vpn_config
    if ! vpn_config=$(get_custom_vpn_config); then
        return 1
    fi
    
    # Parse the configuration
    IFS='|' read -r vpn_endpoint_ip vpn_endpoint_port wireguard_public_key \
        wireguard_private_key wireguard_addresses dns_servers <<< "$vpn_config"

    # Create and start the Gluetun container with custom configuration
    if create_custom_container "$container_name" "$port_mappings" "$ports" \
        "$vpn_endpoint_ip" "$vpn_endpoint_port" "$wireguard_public_key" \
        "$wireguard_private_key" "$wireguard_addresses" "$dns_servers"; then
        
        check_container_status "$container_name" "$ports" "$dns_servers"
    else
        echo "Error: Container failed to start"
        return 1
    fi
}

# Get custom VPN configuration from user
get_custom_vpn_config() {
    echo "Enter custom WireGuard configuration details:"
    
    # Set VPN_SERVICE_PROVIDER to custom by default
    local vpn_service_provider="custom"
    echo "VPN Service Provider: custom (set by default)"
    
    # Set VPN_TYPE to wireguard by default
    local vpn_type="wireguard"
    echo "VPN Type: wireguard (set by default)"
    
    # Get VPN endpoint details
    local vpn_endpoint_ip
    while true; do
        read -p "Enter VPN Endpoint IP: " vpn_endpoint_ip
        if [[ $vpn_endpoint_ip =~ ^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
            break
        else
            echo "Error: Please enter a valid IP address (e.g., ***********)"
        fi
    done
    
    local vpn_endpoint_port
    while true; do
        read -p "Enter VPN Endpoint Port: " vpn_endpoint_port
        if [[ $vpn_endpoint_port =~ ^[0-9]+$ ]] && [ "$vpn_endpoint_port" -ge 1 ] && [ "$vpn_endpoint_port" -le 65535 ]; then
            break
        else
            echo "Error: Please enter a valid port number (1-65535)"
        fi
    done
    
    # Get WireGuard keys
    local wireguard_public_key
    while true; do
        read -p "Enter WireGuard Public Key: " wireguard_public_key
        if [ -n "$wireguard_public_key" ]; then
            break
        else
            echo "Error: Public key cannot be empty"
        fi
    done
    
    local wireguard_private_key
    while true; do
        read -p "Enter WireGuard Private Key: " wireguard_private_key
        if [ -n "$wireguard_private_key" ]; then
            break
        else
            echo "Error: Private key cannot be empty"
        fi
    done
    
    # Get WireGuard addresses
    local wireguard_addresses
    while true; do
        read -p "Enter WireGuard Addresses (e.g., ********/32): " wireguard_addresses
        if [[ $wireguard_addresses =~ ^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+/[0-9]+$ ]]; then
            break
        else
            echo "Error: Please enter a valid CIDR address (e.g., ********/32)"
        fi
    done
    
    # Get DNS servers
    local dns_servers
    while true; do
        read -p "Enter DNS server(s) (space separated, e.g., ******* *******): " dns_servers
        if [ -n "$dns_servers" ]; then
            # Validate each DNS server
            local valid=true
            for dns in $dns_servers; do
                if ! [[ $dns =~ ^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
                    valid=false
                    break
                fi
            done
            
            if $valid; then
                break
            else
                echo "Error: Please enter valid IP addresses for DNS servers"
            fi
        else
            echo "Error: At least one DNS server is required"
        fi
    done
    
    # Return all configuration values separated by pipes
    echo "${vpn_endpoint_ip}|${vpn_endpoint_port}|${wireguard_public_key}|${wireguard_private_key}|${wireguard_addresses}|${dns_servers}"
    return 0
}

# Create custom Gluetun container
create_custom_container() {
    local container_name="$1"
    local port_mappings="$2"
    local ports="$3"
    local vpn_endpoint_ip="$4"
    local vpn_endpoint_port="$5"
    local wireguard_public_key="$6"
    local wireguard_private_key="$7"
    local wireguard_addresses="$8"
    local dns_servers="$9"
    
    # Get common Docker parameters and environment variables
    local common_params=$(get_common_docker_params)
    local common_env=$(get_common_env_vars "$ports")
    
    # Create and start the container
    docker run -d \
        --name="${container_name}" \
        $common_params \
        -e VPN_SERVICE_PROVIDER=custom \
        -e VPN_TYPE=wireguard \
        -e WIREGUARD_ENDPOINT_IP="${vpn_endpoint_ip}" \
        -e WIREGUARD_ENDPOINT_PORT="${vpn_endpoint_port}" \
        -e WIREGUARD_PUBLIC_KEY="${wireguard_public_key}" \
        -e WIREGUARD_PRIVATE_KEY="${wireguard_private_key}" \
        -e WIREGUARD_ADDRESSES="${wireguard_addresses}" \
        -e DNS="$(echo ${dns_servers} | tr ' ' ',')" \
        $common_env \
        ${port_mappings} \
        $DOCKER_NETWORK \
        qmcgaw/gluetun:latest

    return $?
}
