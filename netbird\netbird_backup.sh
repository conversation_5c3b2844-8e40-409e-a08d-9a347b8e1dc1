#!/bin/bash
# Netbird backup functions loader
# This file now serves as a compatibility layer that sources the modular backup components

# Get the directory where this script is located
NETBIRD_BACKUP_DIR="$(dirname "${BASH_SOURCE[0]}")"

# Source the modular backup components
source "$NETBIRD_BACKUP_DIR/netbird_backup_config.sh"
source "$NETBIRD_BACKUP_DIR/netbird_backup_data.sh"

# All backup functions are now available through their respective specialized modules:
# - netbird_backup_config.sh: Configuration backup functions and utilities
# - netbird_backup_data.sh: Data and volume backup functions, including full backup

# This modular approach keeps file sizes manageable while maintaining all existing functionality
