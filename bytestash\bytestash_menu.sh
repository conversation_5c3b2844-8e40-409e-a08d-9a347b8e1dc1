#!/bin/bash

# ByteStash menu functions
# This file contains the menu interfaces and UI functions

# Get the directory where this script is located
BYTESTASH_MENU_DIR="$(dirname "${BASH_SOURCE[0]}")"

# Source required modules for menu functionality
source "$BYTESTASH_MENU_DIR/bytestash_functions.sh"

# Main ByteStash menu function
bytestash_menu() {
    while true; do
        # Show current status
        echo ""
        if [ -d "$BYTESTASH_DIR" ] && check_container_exists "$BYTESTASH_CONTAINER_NAME"; then
            if docker ps --format '{{.Names}}' | grep -q "^$BYTESTASH_CONTAINER_NAME$"; then
                echo -e "${GREEN}ByteStash Status: Running${NC}"
                echo -e "${CYAN}Access URL: http://$server_ip:5000${NC}"
            else
                echo -e "${YELLOW}ByteStash Status: Installed but not running${NC}"
            fi
        elif [ -d "$BYTESTASH_DIR" ]; then
            echo -e "${YELLOW}ByteStash Status: Installed but container missing${NC}"
        else
            echo -e "${RED}ByteStash Status: Not installed${NC}"
        fi
        echo ""

        options=(
            "01. Install ByteStash"
            "02. Remove ByteStash"
            "03. Backup ByteStash"
            "04. Restore ByteStash"
            "05. Check Status"
            "06. Back to Main Menu"
        )

        create_menu "ByteStash Management Menu" "${options[@]}"

        read -rp "$(echo -e ${YELLOW}"Enter your choice: "${NC})" choice

        case $choice in
            1)
                echo "Starting ByteStash installation..."
                if install_bytestash; then
                    echo -e "${GREEN}ByteStash installation completed successfully.${NC}"
                else
                    echo -e "${RED}ByteStash installation failed.${NC}"
                fi
                ;;
            2)
                echo "Starting ByteStash removal..."
                if remove_bytestash; then
                    echo -e "${GREEN}ByteStash removal completed successfully.${NC}"
                else
                    echo -e "${RED}ByteStash removal failed.${NC}"
                fi
                ;;
            3)
                echo "Starting ByteStash backup..."
                if backup_bytestash; then
                    echo -e "${GREEN}ByteStash backup completed successfully.${NC}"
                else
                    echo -e "${RED}ByteStash backup failed.${NC}"
                fi
                ;;
            4)
                echo "Starting ByteStash restore..."
                if restore_bytestash; then
                    echo -e "${GREEN}ByteStash restore completed successfully.${NC}"
                else
                    echo -e "${RED}ByteStash restore failed.${NC}"
                fi
                ;;
            5) check_bytestash_status ;;
            6) return ;;
            *) echo -e "${RED}Invalid choice. Please try again.${NC}" ;;
        esac

        echo  # Add a blank line for better readability
    done
}
