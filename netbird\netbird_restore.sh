#!/bin/bash

# Netbird restore functions for configuration, data, and volumes

# Function to restore Netbird configuration
restore_netbird_config() {
    echo "Restoring Netbird configuration..."
    echo "================================="

    local backup_dir="/opt/netbird_backups"
    local rclone_source="$RCLONE_REMOTE:/Backups/Netbird"

    # Check if rclone is installed
    if ! install_rclone; then
        echo "Failed to verify/install Rclone. Restore cannot proceed."
        return 1
    fi

    # List available config backups
    echo "Looking for configuration backups..."
    local config_backups=$(rclone ls "$rclone_source" 2>/dev/null | grep "netbird_config_backup_" | awk '{print $2}')

    if [ -z "$config_backups" ]; then
        echo "No configuration backups found in cloud storage."
        return 1
    fi

    echo "Available configuration backups:"
    echo "$config_backups" | nl

    echo ""
    read -rp "Enter the number of the backup to restore (or 'q' to quit): " backup_choice

    if [[ "$backup_choice" == "q" ]]; then
        echo "Restore cancelled."
        return 1
    fi

    local selected_backup=$(echo "$config_backups" | sed -n "${backup_choice}p")
    if [ -z "$selected_backup" ]; then
        echo "Invalid selection."
        return 1
    fi

    echo "Selected backup: $selected_backup"

    # Download backup
    mkdir -p "$backup_dir"
    local local_backup_path="$backup_dir/$selected_backup"

    echo "Downloading configuration backup..."
    if ! rclone copy "$rclone_source/$selected_backup" "$backup_dir" --progress; then
        echo "Failed to download backup."
        return 1
    fi

    # Determine restore location
    local restore_path="/opt/netbird"
    read -rp "Enter restore path (default: $restore_path): " user_path
    if [ -n "$user_path" ]; then
        restore_path="$user_path"
    fi

    # Create restore directory
    sudo mkdir -p "$restore_path"

    # Extract backup
    echo "Extracting configuration backup to $restore_path..."
    if tar -xzf "$local_backup_path" -C "$restore_path"; then
        echo "Configuration restored successfully to $restore_path"
        rm -f "$local_backup_path"
        return 0
    else
        echo "Failed to extract configuration backup."
        rm -f "$local_backup_path"
        return 1
    fi
}

# Function to restore Netbird data
restore_netbird_data() {
    echo "Restoring Netbird data..."
    echo "========================"

    local backup_dir="/opt/netbird_backups"
    local rclone_source="$RCLONE_REMOTE:/Backups/Netbird"

    # Check if rclone is installed
    if ! install_rclone; then
        echo "Failed to verify/install Rclone. Restore cannot proceed."
        return 1
    fi

    # List available data backups
    echo "Looking for data backups..."
    local data_backups=$(rclone ls "$rclone_source" 2>/dev/null | grep "netbird_data_backup_" | awk '{print $2}')

    if [ -z "$data_backups" ]; then
        echo "No data backups found in cloud storage."
        return 1
    fi

    echo "Available data backups:"
    echo "$data_backups" | nl

    echo ""
    read -rp "Enter the number of the backup to restore (or 'q' to quit): " backup_choice

    if [[ "$backup_choice" == "q" ]]; then
        echo "Restore cancelled."
        return 1
    fi

    local selected_backup=$(echo "$data_backups" | sed -n "${backup_choice}p")
    if [ -z "$selected_backup" ]; then
        echo "Invalid selection."
        return 1
    fi

    echo "Selected backup: $selected_backup"

    # Download backup
    mkdir -p "$backup_dir"
    local local_backup_path="$backup_dir/$selected_backup"

    echo "Downloading data backup..."
    if ! rclone copy "$rclone_source/$selected_backup" "$backup_dir" --progress; then
        echo "Failed to download backup."
        return 1
    fi

    # Find Netbird installation
    local netbird_path=$(detect_netbird_installation)
    if [ -z "$netbird_path" ]; then
        echo "Netbird installation not found. Please install Netbird first."
        rm -f "$local_backup_path"
        return 1
    fi

    echo "Restoring to Netbird installation at: $netbird_path"

    # Stop services
    echo "Stopping Netbird services..."
    cd "$netbird_path"
    docker compose stop

    # Extract backup to temporary location
    local temp_restore_dir="$backup_dir/restore_temp"
    mkdir -p "$temp_restore_dir"

    echo "Extracting data backup..."
    if tar -xzf "$local_backup_path" -C "$temp_restore_dir"; then
        echo "Data backup extracted successfully."

        # Restore management data
        if [ -d "$temp_restore_dir/management_data" ]; then
            echo "Restoring management data..."
            # This would typically involve copying data back to the management container
            # Implementation depends on specific container setup
        fi

        # Restore database
        if [ -f "$temp_restore_dir/zitadel_db/zitadel_backup.sql" ]; then
            echo "Restoring Zitadel database..."
            
            # Start database container
            docker compose up -d zdb 2>/dev/null || docker compose up -d postgres 2>/dev/null
            sleep 15

            # Restore database
            local db_container="zdb"
            if ! docker compose ps --format "{{.Service}}" | grep -q "^zdb$"; then
                db_container="postgres"
            fi

            if docker compose exec -T "$db_container" psql -U postgres < "$temp_restore_dir/zitadel_db/zitadel_backup.sql" 2>/dev/null; then
                echo "Database restored successfully."
            else
                echo "Warning: Database restore may have failed."
            fi
        fi

        # Restore certificates and keys
        if [ -d "$temp_restore_dir/machinekey" ]; then
            echo "Restoring machine keys..."
            cp -r "$temp_restore_dir/machinekey" "$netbird_path/" 2>/dev/null
        fi

        # Restore other data directories
        for dir in "certs" "ssl" "data" "logs"; do
            if [ -d "$temp_restore_dir/$dir" ]; then
                echo "Restoring $dir..."
                cp -r "$temp_restore_dir/$dir" "$netbird_path/" 2>/dev/null
            fi
        done

        # Cleanup
        rm -rf "$temp_restore_dir"
        rm -f "$local_backup_path"

        # Restart services
        echo "Restarting Netbird services..."
        docker compose up -d

        echo "Data restore completed successfully."
        return 0
    else
        echo "Failed to extract data backup."
        rm -rf "$temp_restore_dir"
        rm -f "$local_backup_path"
        cd "$netbird_path"
        docker compose up -d
        return 1
    fi
}

# Function to restore Docker volumes
restore_netbird_volumes() {
    echo "Restoring Netbird volumes..."
    echo "==========================="

    local backup_dir="/opt/netbird_backups"
    local rclone_source="$RCLONE_REMOTE:/Backups/Netbird"

    # Check if rclone is installed
    if ! install_rclone; then
        echo "Failed to verify/install Rclone. Restore cannot proceed."
        return 1
    fi

    # List available volume backups
    echo "Looking for volume backups..."
    local volume_backups=$(rclone ls "$rclone_source" 2>/dev/null | grep "netbird_volumes_backup_" | awk '{print $2}')

    if [ -z "$volume_backups" ]; then
        echo "No volume backups found in cloud storage."
        return 1
    fi

    echo "Available volume backups:"
    echo "$volume_backups" | nl

    echo ""
    read -rp "Enter the number of the backup to restore (or 'q' to quit): " backup_choice

    if [[ "$backup_choice" == "q" ]]; then
        echo "Restore cancelled."
        return 1
    fi

    local selected_backup=$(echo "$volume_backups" | sed -n "${backup_choice}p")
    if [ -z "$selected_backup" ]; then
        echo "Invalid selection."
        return 1
    fi

    echo "Selected backup: $selected_backup"

    # Download backup
    mkdir -p "$backup_dir"
    local local_backup_path="$backup_dir/$selected_backup"

    echo "Downloading volume backup..."
    if ! rclone copy "$rclone_source/$selected_backup" "$backup_dir" --progress; then
        echo "Failed to download backup."
        return 1
    fi

    # Find Netbird installation
    local netbird_path=$(detect_netbird_installation)
    if [ -z "$netbird_path" ]; then
        echo "Netbird installation not found. Please install Netbird first."
        rm -f "$local_backup_path"
        return 1
    fi

    echo "Restoring to Netbird installation at: $netbird_path"

    # Stop services
    echo "Stopping Netbird services..."
    cd "$netbird_path"
    docker compose stop

    # Extract backup
    local temp_restore_dir="$backup_dir/volumes_restore_temp"
    mkdir -p "$temp_restore_dir"

    echo "Extracting volume backup..."
    if tar -xzf "$local_backup_path" -C "$temp_restore_dir"; then
        echo "Volume backup extracted successfully."

        # Restore each volume
        for volume_dir in "$temp_restore_dir"/*; do
            if [ -d "$volume_dir" ]; then
                local volume_name=$(basename "$volume_dir")
                echo "Restoring volume: $volume_name"

                # Create volume if it doesn't exist
                docker volume create "$volume_name" >/dev/null 2>&1

                # Restore volume data
                if [ -f "$volume_dir/volume_data.tar.gz" ]; then
                    docker run --rm -v "$volume_name:/volume" -v "$volume_dir:/backup" alpine:latest \
                        sh -c "cd /volume && tar -xzf /backup/volume_data.tar.gz" 2>/dev/null && \
                        echo "Successfully restored volume: $volume_name" || \
                        echo "Warning: Failed to restore volume: $volume_name"
                fi
            fi
        done

        # Cleanup
        rm -rf "$temp_restore_dir"
        rm -f "$local_backup_path"

        # Restart services
        echo "Restarting Netbird services..."
        docker compose up -d

        echo "Volume restore completed successfully."
        return 0
    else
        echo "Failed to extract volume backup."
        rm -rf "$temp_restore_dir"
        rm -f "$local_backup_path"
        cd "$netbird_path"
        docker compose up -d
        return 1
    fi
}

# Function to perform full Netbird restore
restore_netbird_full() {
    echo "Starting COMPLETE Netbird restore..."
    echo "===================================="
    echo "This will restore:"
    echo "- Configuration files"
    echo "- Application data and databases"
    echo "- Docker volumes"
    echo ""

    local success=true

    # Restore configuration
    echo "Step 1/3: Restoring configuration files..."
    if ! restore_netbird_config; then
        echo "Configuration restore failed."
        success=false
    fi
    echo ""

    # Restore data
    echo "Step 2/3: Restoring application data..."
    if ! restore_netbird_data; then
        echo "Data restore failed."
        success=false
    fi
    echo ""

    # Restore volumes
    echo "Step 3/3: Restoring Docker volumes..."
    if ! restore_netbird_volumes; then
        echo "Volume restore failed."
        success=false
    fi
    echo ""

    if $success; then
        echo "========================================="
        echo "COMPLETE Netbird restore finished successfully!"
        echo "========================================="
        echo "Your Netbird installation should now be fully restored."
        return 0
    else
        echo "========================================="
        echo "COMPLETE Netbird restore finished with errors!"
        echo "========================================="
        echo "Some components may not have been restored properly."
        echo "Check the output above for details."
        return 1
    fi
}

# Function to restore from domain-based backup
restore_netbird_from_domain() {
    echo "Restore from Domain-based Backup"
    echo "================================"

    local rclone_source="$RCLONE_REMOTE:/Backups/Netbird"

    # Check if rclone is installed
    if ! install_rclone; then
        echo "Failed to verify/install Rclone. Restore cannot proceed."
        return 1
    fi

    # Get list of available domains
    echo "Looking for available domain backups..."
    local domains=$(rclone lsd "$rclone_source" 2>/dev/null | awk '{print $5}' | grep -v "^$")

    if [ -z "$domains" ]; then
        echo "No domain-based backups found in cloud storage."
        echo ""
        echo "This could mean:"
        echo "- No domain-based backups have been created yet"
        echo "- You may have legacy backups (use options 1-4 in restore menu)"
        echo "- Rclone configuration issue"
        return 1
    fi

    echo "Available domains with backups:"
    echo "==============================="

    local domain_array=()
    local count=0
    while IFS= read -r domain; do
        if [ -n "$domain" ]; then
            ((count++))
            domain_array+=("$domain")
            echo "$count. $domain"

            # Show what backup types are available for this domain
            local domain_path="$rclone_source/$domain"
            local config_backup=$(rclone ls "$domain_path" 2>/dev/null | grep "netbird_config_backup_" | wc -l)
            local data_backup=$(rclone ls "$domain_path" 2>/dev/null | grep "netbird_data_backup_" | wc -l)
            local volumes_backup=$(rclone ls "$domain_path" 2>/dev/null | grep "netbird_volumes_backup_" | wc -l)

            echo "   Available backups:"
            [ $config_backup -gt 0 ] && echo "   ✓ Configuration backup"
            [ $data_backup -gt 0 ] && echo "   ✓ Data backup"
            [ $volumes_backup -gt 0 ] && echo "   ✓ Volumes backup"

            if [ $config_backup -eq 0 ] && [ $data_backup -eq 0 ] && [ $volumes_backup -eq 0 ]; then
                echo "   ✗ No backup files found"
            fi
            echo ""
        fi
    done <<< "$domains"

    if [ $count -eq 0 ]; then
        echo "No valid domains found."
        return 1
    fi

    echo ""
    read -rp "Enter the number of the domain to restore from (or 'q' to quit): " domain_choice

    if [[ "$domain_choice" == "q" ]]; then
        echo "Restore cancelled."
        return 1
    fi

    if ! [[ "$domain_choice" =~ ^[0-9]+$ ]] || [ "$domain_choice" -lt 1 ] || [ "$domain_choice" -gt $count ]; then
        echo "Invalid selection."
        return 1
    fi

    local selected_domain="${domain_array[$((domain_choice-1))]}"
    echo "Selected domain: $selected_domain"

    # Confirm restore
    echo ""
    echo "WARNING: This will restore Netbird from the backup of domain: $selected_domain"
    echo "This will overwrite any existing Netbird installation and data!"
    echo ""
    read -rp "Are you sure you want to continue? (y/n): " confirm_restore

    if [[ ! "$confirm_restore" =~ ^[Yy]$ ]]; then
        echo "Restore cancelled."
        return 1
    fi

    # Perform domain-based restore
    local domain_path="$rclone_source/$selected_domain"
    local backup_dir="/opt/netbird_backups"
    mkdir -p "$backup_dir"

    echo ""
    echo "Starting domain-based restore for: $selected_domain"
    echo "=================================================="

    local success=true

    # Download and restore configuration
    echo "Step 1/3: Restoring configuration..."
    local config_backup=$(rclone ls "$domain_path" 2>/dev/null | grep "netbird_config_backup_" | awk '{print $2}' | head -1)
    if [ -n "$config_backup" ]; then
        echo "Downloading configuration backup: $config_backup"
        if rclone copy "$domain_path/$config_backup" "$backup_dir" --progress; then
            # Determine restore location
            local restore_path="/opt/netbird"
            sudo mkdir -p "$restore_path"

            # Extract configuration
            if tar -xzf "$backup_dir/$config_backup" -C "$restore_path"; then
                echo "Configuration restored successfully."
                rm -f "$backup_dir/$config_backup"
            else
                echo "Failed to extract configuration backup."
                success=false
            fi
        else
            echo "Failed to download configuration backup."
            success=false
        fi
    else
        echo "No configuration backup found for this domain."
        success=false
    fi

    echo ""

    # Download and restore data
    echo "Step 2/3: Restoring data..."
    local data_backup=$(rclone ls "$domain_path" 2>/dev/null | grep "netbird_data_backup_" | awk '{print $2}' | head -1)
    if [ -n "$data_backup" ]; then
        echo "Downloading data backup: $data_backup"
        if rclone copy "$domain_path/$data_backup" "$backup_dir" --progress; then
            # Find Netbird installation
            local netbird_path=$(detect_netbird_installation)
            if [ -n "$netbird_path" ]; then
                echo "Restoring data to: $netbird_path"

                # Stop services
                cd "$netbird_path"
                docker compose stop

                # Extract and restore data
                local temp_restore_dir="$backup_dir/restore_temp"
                mkdir -p "$temp_restore_dir"

                if tar -xzf "$backup_dir/$data_backup" -C "$temp_restore_dir"; then
                    # Restore database and other data (simplified version)
                    if [ -f "$temp_restore_dir/zitadel_db/zitadel_backup.sql" ]; then
                        echo "Restoring database..."
                        docker compose up -d zdb 2>/dev/null || docker compose up -d postgres 2>/dev/null
                        sleep 15

                        local db_container="zdb"
                        if ! docker compose ps --format "{{.Service}}" | grep -q "^zdb$"; then
                            db_container="postgres"
                        fi

                        docker compose exec -T "$db_container" psql -U postgres < "$temp_restore_dir/zitadel_db/zitadel_backup.sql" 2>/dev/null || echo "Database restore may have failed."
                    fi

                    # Restore other data
                    for dir in "machinekey" "certs" "ssl" "data" "logs"; do
                        if [ -d "$temp_restore_dir/$dir" ]; then
                            cp -r "$temp_restore_dir/$dir" "$netbird_path/" 2>/dev/null
                        fi
                    done

                    echo "Data restored successfully."
                    rm -rf "$temp_restore_dir"
                    rm -f "$backup_dir/$data_backup"
                else
                    echo "Failed to extract data backup."
                    success=false
                fi
            else
                echo "Netbird installation not found after configuration restore."
                success=false
            fi
        else
            echo "Failed to download data backup."
            success=false
        fi
    else
        echo "No data backup found for this domain."
    fi

    echo ""

    # Download and restore volumes
    echo "Step 3/3: Restoring volumes..."
    local volumes_backup=$(rclone ls "$domain_path" 2>/dev/null | grep "netbird_volumes_backup_" | awk '{print $2}' | head -1)
    if [ -n "$volumes_backup" ]; then
        echo "Downloading volumes backup: $volumes_backup"
        if rclone copy "$domain_path/$volumes_backup" "$backup_dir" --progress; then
            local netbird_path=$(detect_netbird_installation)
            if [ -n "$netbird_path" ]; then
                cd "$netbird_path"
                docker compose stop

                # Extract and restore volumes
                local temp_restore_dir="$backup_dir/volumes_restore_temp"
                mkdir -p "$temp_restore_dir"

                if tar -xzf "$backup_dir/$volumes_backup" -C "$temp_restore_dir"; then
                    # Restore each volume
                    for volume_dir in "$temp_restore_dir"/*; do
                        if [ -d "$volume_dir" ]; then
                            local volume_name=$(basename "$volume_dir")
                            echo "Restoring volume: $volume_name"

                            docker volume create "$volume_name" >/dev/null 2>&1

                            if [ -f "$volume_dir/volume_data.tar.gz" ]; then
                                docker run --rm -v "$volume_name:/volume" -v "$volume_dir:/backup" alpine:latest \
                                    sh -c "cd /volume && tar -xzf /backup/volume_data.tar.gz" 2>/dev/null || echo "Warning: Failed to restore volume: $volume_name"
                            fi
                        fi
                    done

                    echo "Volumes restored successfully."
                    rm -rf "$temp_restore_dir"
                    rm -f "$backup_dir/$volumes_backup"
                else
                    echo "Failed to extract volumes backup."
                    success=false
                fi
            fi
        else
            echo "Failed to download volumes backup."
            success=false
        fi
    else
        echo "No volumes backup found for this domain."
    fi

    # Start services
    local netbird_path=$(detect_netbird_installation)
    if [ -n "$netbird_path" ]; then
        echo ""
        echo "Starting Netbird services..."
        cd "$netbird_path"
        docker compose up -d
    fi

    echo ""
    if $success; then
        echo "========================================="
        echo "Domain-based restore completed successfully!"
        echo "========================================="
        echo "Netbird has been restored from domain: $selected_domain"
        echo "Your installation should now be fully functional."
        return 0
    else
        echo "========================================="
        echo "Domain-based restore completed with errors!"
        echo "========================================="
        echo "Some components may not have been restored properly."
        echo "Check the output above for details."
        return 1
    fi
}
