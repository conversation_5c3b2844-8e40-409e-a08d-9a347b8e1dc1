# ByteStash Module

## Overview
The ByteStash module provides automated installation, management, backup, and restore functionality for ByteStash - a self-hosted code snippet storage solution.

## Features
- **Install ByteStash**: Automated installation using Docker Compose
- **Remove ByteStash**: Clean removal with optional data preservation
- **Backup ByteStash**: Complete backup of configuration and data
- **Restore ByteStash**: Restore from the most recent backup automatically

## Installation Details
- **Installation Path**: `/opt/bytestash`
- **Data Directory**: `/opt/bytestash/data`
- **Container Name**: `bytestash`
- **Port**: 5000
- **Image**: `ghcr.io/jordan-dalby/bytestash:latest`

## Backup Configuration
- **Backup Location**: `Eyunion:Backups/Bytestash` (using global RCLONE_REMOTE)
- **Backup Type**: Complete backup (configuration + data)
- **Backup Format**: Compressed tar.gz archive
- **Naming Convention**: `bytestash-complete-backup_YYYYMMDD_HHMMSS.tar.gz`
- **Backup Retention**: Only one backup is kept (previous backups are automatically removed)

## Default Configuration
The module uses the following default settings:
- JWT Secret: Auto-generated based on timestamp
- Token Expiry: 24 hours
- New Accounts: Enabled
- Debug Mode: Disabled
- Account System: Enabled
- Password Changes: Enabled
- OIDC/SSO: Disabled

## Network Configuration
ByteStash is configured to use the global Docker network `my_network` for integration with other services.

## Menu Options
1. **Install ByteStash**: Fresh installation with default configuration
2. **Remove ByteStash**: Remove container with optional data deletion
3. **Backup ByteStash**: Create complete backup and upload to cloud storage
4. **Restore ByteStash**: Download and restore from most recent backup
5. **Check Status**: Display current installation and running status
6. **Back to Main Menu**: Return to main application menu

## Files
- `bytestash_functions.sh`: Core functionality and installation logic
- `bytestash_menu.sh`: User interface and menu system
- `bytestash_module.md`: This documentation file

## Dependencies
- Docker and Docker Compose
- Rclone (for backup/restore functionality)
- Global network configuration (`my_network`)

## Access
After installation, ByteStash can be accessed at:
- **URL**: http://[server_ip]:5000 (uses the global server_ip variable)
- **Default**: No authentication required initially (can be configured)

## Notes
- All operations use default settings without user prompts as requested
- Backup and restore operations are fully automated
- Only one backup is maintained in cloud storage (previous backups are automatically removed)
- Access URLs use the global server_ip variable for consistency with other modules
- The module follows the same patterns as other modules in the project
- Data is stored persistently in `/opt/bytestash/data`
