#!/bin/bash

# Netbird installation, removal, and update functions

# Function to check Netbird installation prerequisites
check_netbird_prerequisites() {
    echo "Checking Netbird installation prerequisites..."

    local missing_requirements=()

    # Check if running as root or with sudo access
    if [ "$EUID" -ne 0 ] && ! sudo -n true 2>/dev/null; then
        echo "This script requires root privileges or sudo access."
        return 1
    fi

    # Check available disk space (need at least 2GB)
    local available_space=$(df / | awk 'NR==2 {print $4}')
    if [ "$available_space" -lt 2097152 ]; then  # 2GB in KB
        missing_requirements+=("Insufficient disk space (need at least 2GB)")
    fi

    # Check if ports are available
    local required_ports=(80 443 33073 10000 33080 3478)
    for port in "${required_ports[@]}"; do
        if netstat -tuln 2>/dev/null | grep -q ":$port "; then
            missing_requirements+=("Port $port is already in use")
        fi
    done

    if [ ${#missing_requirements[@]} -gt 0 ]; then
        echo "Prerequisites check failed:"
        printf '%s\n' "${missing_requirements[@]}"
        return 1
    fi

    echo "Prerequisites check passed."
    return 0
}

# Function to install Netbird
install_netbird() {
    local install_path="/opt/netbird"

    echo "Installing Netbird..."
    echo "===================="

    # Check prerequisites first
    if ! check_netbird_prerequisites; then
        echo "Prerequisites check failed. Please resolve the issues above."
        return 1
    fi

    # Check if already installed
    if [ -d "$install_path" ] && [ -f "$install_path/docker-compose.yml" ]; then
        echo "Netbird appears to be already installed at $install_path"
        read -rp "Would you like to reinstall? This will remove the existing installation. (y/n): " reinstall_choice
        if [[ "$reinstall_choice" =~ ^[Yy]$ ]]; then
            if ! remove_netbird; then
                echo "Failed to remove existing installation."
                return 1
            fi
        else
            echo "Installation cancelled."
            return 1
        fi
    fi

    # Ensure Docker is installed
    if ! install_docker; then
        echo "Docker installation failed. Cannot proceed."
        return 1
    fi

    # Check required tools
    if ! command -v curl >/dev/null 2>&1; then
        echo "Installing curl..."
        if command -v apt-get >/dev/null 2>&1; then
            sudo apt-get update && sudo apt-get install -y curl
        elif command -v yum >/dev/null 2>&1; then
            sudo yum install -y curl
        else
            echo "Please install curl manually."
            return 1
        fi
    fi

    if ! command -v jq >/dev/null 2>&1; then
        echo "Installing jq..."
        if command -v apt-get >/dev/null 2>&1; then
            sudo apt-get update && sudo apt-get install -y jq
        elif command -v yum >/dev/null 2>&1; then
            sudo yum install -y jq
        else
            echo "Please install jq manually."
            return 1
        fi
    fi

    # Get domain name
    echo ""
    echo "Netbird requires a domain name for proper operation."
    echo "The domain should point to this server's public IP address."
    echo "Example: netbird.yourdomain.com"
    echo ""

    local netbird_domain=""
    while [ -z "$netbird_domain" ]; do
        read -rp "Enter your domain name: " netbird_domain

        if [ -z "$netbird_domain" ]; then
            echo "Domain name is required. Please enter a valid domain."
            continue
        fi

        # Basic domain validation
        if [[ ! "$netbird_domain" =~ ^[a-zA-Z0-9][a-zA-Z0-9.-]*[a-zA-Z0-9]\.[a-zA-Z]{2,}$ ]]; then
            echo "Invalid domain format. Please use a valid domain name (e.g., netbird.example.com)."
            netbird_domain=""
            continue
        fi

        # Check if domain is reachable (optional warning)
        echo "Checking domain accessibility..."
        if ! nslookup "$netbird_domain" >/dev/null 2>&1; then
            echo "Warning: Domain '$netbird_domain' does not resolve to an IP address."
            read -rp "Continue anyway? (y/n): " continue_choice
            if [[ ! "$continue_choice" =~ ^[Yy]$ ]]; then
                netbird_domain=""
                continue
            fi
        fi

        echo "Using domain: $netbird_domain"
        break
    done

    # Create installation directory
    echo "Creating installation directory at $install_path..."
    sudo mkdir -p "$install_path"

    # Change to installation directory
    cd "$install_path" || {
        echo "Failed to change to installation directory."
        return 1
    }

    # Set environment variable and download installation script
    echo "Downloading and running Netbird installation script..."
    echo "This may take several minutes..."

    export NETBIRD_DOMAIN="$netbird_domain"

    # Download and run the official installation script with error handling
    echo "Downloading installation script..."
    local install_script="/tmp/netbird-install.sh"

    if curl -fsSL https://github.com/netbirdio/netbird/releases/latest/download/getting-started-with-zitadel.sh -o "$install_script"; then
        echo "Running installation script..."
        chmod +x "$install_script"
        if sudo -E bash "$install_script"; then
        echo ""
        echo "Netbird installation completed successfully!"
        echo "========================================"
        echo "Domain: $netbird_domain"
        echo "Installation path: $install_path"
        echo ""
        echo "You can access Netbird at: https://$netbird_domain"
        echo ""

        # Look for credentials file
        if [ -f "$install_path/.env" ]; then
            echo "Login credentials:"
            cat "$install_path/.env"
        else
            echo "Check the installation directory for login credentials."
        fi

            echo ""
            echo "Installation completed. You can now manage Netbird through this menu."
            rm -f "$install_script"
            return 0
        else
            echo "Netbird installation script failed."
            echo "Check the output above for error details."
            rm -f "$install_script"
            return 1
        fi
    else
        echo "Failed to download Netbird installation script."
        echo "Please check your internet connection and try again."
        return 1
    fi
}

# Function to remove Netbird installation
remove_netbird() {
    echo "Removing Netbird installation..."
    echo "==============================="

    local netbird_path=$(detect_netbird_installation)

    if [ -z "$netbird_path" ]; then
        echo "Netbird installation not found."
        return 1
    fi

    echo "Found Netbird installation at: $netbird_path"

    # Confirm removal
    echo "WARNING: This will completely remove Netbird and all its data!"
    read -rp "Are you sure you want to continue? (y/n): " confirm_removal
    if [[ ! "$confirm_removal" =~ ^[Yy]$ ]]; then
        echo "Removal cancelled."
        return 1
    fi

    # Offer backup before removal
    read -rp "Would you like to create a backup before removal? (y/n): " backup_choice
    if [[ "$backup_choice" =~ ^[Yy]$ ]]; then
        echo "Creating backup before removal..."
        if ! backup_netbird_full; then
            read -rp "Backup failed. Continue with removal anyway? (y/n): " continue_choice
            if [[ ! "$continue_choice" =~ ^[Yy]$ ]]; then
                echo "Removal cancelled."
                return 1
            fi
        fi
    fi

    cd "$netbird_path"

    # Stop and remove containers
    echo "Stopping and removing Netbird containers..."
    if [ -f "docker-compose.yml" ]; then
        docker compose down --volumes --remove-orphans
    fi

    # Remove Docker images
    echo "Removing Netbird Docker images..."
    docker images | grep -E "netbird|zitadel|caddy|postgres|cockroach" | awk '{print $3}' | xargs -r docker rmi --force

    # Remove installation directory
    echo "Removing installation directory..."
    cd /
    sudo rm -rf "$netbird_path"

    # Remove backup directory if empty
    if [ -d "/opt/netbird_backups" ] && [ -z "$(ls -A /opt/netbird_backups)" ]; then
        echo "Removing empty backup directory..."
        sudo rm -rf "/opt/netbird_backups"
    fi

    echo "Netbird has been completely removed."
    return 0
}

# Function to update Netbird installation
update_netbird() {
    local netbird_path=$(detect_netbird_installation)

    if [ -z "$netbird_path" ]; then
        echo "Netbird installation not found."
        return 1
    fi

    echo "Updating Netbird installation..."
    echo "==============================="

    # Backup before update
    read -rp "Would you like to create a backup before updating? (y/n): " backup_choice
    if [[ "$backup_choice" =~ ^[Yy]$ ]]; then
        if ! backup_netbird_full; then
            read -rp "Backup failed. Continue with update anyway? (y/n): " continue_choice
            if [[ ! "$continue_choice" =~ ^[Yy]$ ]]; then
                echo "Update cancelled."
                return 1
            fi
        fi
    fi

    cd "$netbird_path"

    # Pull latest images
    echo "Pulling latest Netbird images..."
    if docker compose pull; then
        echo "Restarting services with updated images..."
        if docker compose up -d; then
            echo "Netbird update completed successfully."
            return 0
        else
            echo "Failed to restart services after update."
            return 1
        fi
    else
        echo "Failed to pull updated images."
        return 1
    fi
}
