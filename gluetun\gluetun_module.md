# Gluetun Module

This module provides functionality for setting up and managing Gluetun, a VPN client Docker container with a focus on privacy and security. It supports multiple VPN providers including Private Internet Access (PIA), FastestVPN, and custom configurations.

## Modular Architecture

The Gluetun module has been refactored into a modular architecture for better maintainability and organization. Each module focuses on specific functionality while maintaining backward compatibility.

## Files

### gluetun_functions.sh
Main entry point that sources all modular components. This file maintains backward compatibility by loading all specialized modules and providing a unified interface.

### gluetun_core.sh
Core utilities and shared functions used across all Gluetun modules.

**Key Functions:**
- `get_next_gluetun_number()`: Gets the next available number for Gluetun container naming
- `get_gluetun_port()`: Gets the port configuration for a Gluetun container
- `validate_ports()`: Validates port number inputs
- `validate_container_name()`: Validates container name format
- `get_container_name()`: Gets container name from user with validation
- `get_port_configuration()`: Gets port configuration from user
- `check_existing_containers()`: Checks for existing containers and prompts user
- `check_container_status()`: Waits for container initialization and checks status

### gluetun_install_pia.sh
PIA VPN installation functionality.

**Key Functions:**
- `install_gluetun_pia()`: Installs and configures Gluetun with PIA VPN settings
- `parse_pia_config()`: Parses PIA WireGuard configuration files
- `create_pia_container()`: Creates PIA-configured Gluetun container

### gluetun_install_fastestvpn.sh
FastestVPN installation functionality.

**Key Functions:**
- `install_gluetun_fastestvpn()`: Installs and configures Gluetun with FastestVPN settings
- `select_fastestvpn_server()`: Handles FastestVPN server selection from JSON list
- `create_fastestvpn_container()`: Creates FastestVPN-configured Gluetun container

### gluetun_install_custom.sh
Custom VPN installation functionality.

**Key Functions:**
- `install_gluetun_custom()`: Installs and configures Gluetun with custom VPN settings
- `get_custom_vpn_config()`: Gets custom VPN configuration from user input
- `create_custom_container()`: Creates custom-configured Gluetun container

### gluetun_management.sh
Container management and removal functions.

**Key Functions:**
- `remove_gluetun()`: Removes Gluetun container and its dependent containers
- `select_gluetun_container()`: Interactive container selection
- `handle_dependent_containers()`: Manages containers using Gluetun's network
- `get_gluetun_status()`: Gets status of all Gluetun containers
- `start_gluetun()`, `stop_gluetun()`, `restart_gluetun()`: Container lifecycle management

### pia_config.sh
Functions for Private Internet Access (PIA) VPN configuration with Gluetun.

**Key Functions:**
- `create_pia_generator_file()`: Creates configuration generator file for PIA
- `check_pia()`: Checks PIA VPN connection status
- `generate_pia_wireguard_file()`: Generates WireGuard configuration file for PIA

### gluetun_menu.sh
Provides menu interface for Gluetun operations.

**Menu Options:**
1. Add New Gluetun Container with PIA Config
2. Add New Gluetun Container with FastestVPN Config
3. Add New Gluetun Container with Custom Config
4. Remove Gluetun Container
5. Generate PIA Wireguard Config
6. Return to Main Menu
7. Exit

### fastestvpn_server_list.json
JSON file containing FastestVPN server list with country, city, IP address, and domain information for server selection.

## Features

- **Modular Architecture**: Clean separation of concerns with focused modules
- **Multiple VPN Providers**: Support for PIA, FastestVPN, and custom configurations
- **WireGuard Protocol**: Secure and fast VPN protocol support
- **Automatic Management**: Container naming, port management, and dependency handling
- **Docker Integration**: Seamless integration with Docker networking
- **Health Monitoring**: Automatic health checks and reconnection capabilities
- **Flexible Configuration**: Customizable DNS settings and port forwarding
- **Multiple Containers**: Support for multiple concurrent Gluetun instances
- **Backward Compatibility**: All existing function calls continue to work

## Usage

The Gluetun module provides functionality for setting up secure VPN tunnels in Docker:

1. Select "Gluetun Menu" from the main menu
2. Choose your preferred VPN provider configuration:
   - **PIA (Private Internet Access)**: Automatically generates WireGuard configuration
   - **FastestVPN**: Select from a comprehensive server list
   - **Custom configuration**: Enter your own VPN settings
3. Follow the interactive prompts to configure:
   - Container name (auto-generated or custom)
   - Port mappings (single or multiple ports)
   - DNS settings (provider-specific or custom)
   - Network settings and security options
4. The container will be created and started automatically with health monitoring

## Modular Benefits

- **Maintainability**: Each module is focused and manageable (200-300 lines)
- **Testability**: Individual components can be tested separately
- **Extensibility**: New VPN providers can be added as separate modules
- **Code Reuse**: Common utilities are shared across modules
- **Error Isolation**: Issues in one module don't affect others

## Integration with Other Modules

The Gluetun module integrates with:
- **Docker module**: For container management and networking
- **SOCKS5 Gluetun module**: For creating SOCKS5 proxies using Gluetun's network
- **JDownloader module**: For secure downloads through VPN tunnels
- **Other modules**: Can use Gluetun containers as network proxies for secure communication

## Security Features

- **Kill Switch**: Automatic traffic blocking to prevent leaks
- **DNS Leak Protection**: Secure DNS resolution through VPN
- **Custom DNS Support**: Configure preferred DNS servers
- **Network Isolation**: Complete traffic isolation through Docker
- **WireGuard Security**: Modern cryptography and secure key exchange
- **Health Monitoring**: Continuous connection monitoring and automatic recovery
- **Dependency Management**: Safe removal of containers with network dependencies