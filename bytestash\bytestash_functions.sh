#!/bin/bash

# ByteStash functions
# This file contains the core functions for ByteStash management

set -e

# Global configuration
BYTESTASH_DIR="/opt/bytestash"
BYTESTASH_CONTAINER_NAME="bytestash"
BYTESTASH_IMAGE="ghcr.io/jordan-dalby/bytestash:latest"
BYTESTASH_PORT="5000"
BYTESTASH_BACKUP_DIR="$RCLONE_REMOTE:/Backups/Bytestash"

# Common utility functions
check_container_exists() {
    docker ps -a --format '{{.Names}}' | grep -q "^$1$"
}

stop_and_remove_container() {
    echo "Stopping and removing existing $1 container..."
    docker stop "$1" 2>/dev/null || true
    docker rm "$1" 2>/dev/null || true
}

# Install ByteStash function
install_bytestash() {
    echo "Installing ByteStash..."
    
    # Install Docker if not present
    install_docker
    
    # Create ByteStash directory
    echo "Creating ByteStash directory at $BYTESTASH_DIR..."
    sudo mkdir -p "$BYTESTASH_DIR/data"
    sudo chown -R $USER:$USER "$BYTESTASH_DIR"
    
    # Stop and remove existing container if it exists
    if check_container_exists "$BYTESTASH_CONTAINER_NAME"; then
        stop_and_remove_container "$BYTESTASH_CONTAINER_NAME"
    fi
    
    # Create docker-compose.yml file
    echo "Creating docker-compose.yml..."
    local jwt_secret="bytestash-secret-$(date +%s)"
    cat > "$BYTESTASH_DIR/docker-compose.yml" << EOF
services:
  bytestash:
    image: ghcr.io/jordan-dalby/bytestash:latest
    container_name: bytestash
    restart: unless-stopped
    ports:
      - "5000:5000"
    volumes:
      - ./data:/data/snippets
    environment:
      BASE_PATH: ""
      JWT_SECRET: "$jwt_secret"
      TOKEN_EXPIRY: "24h"
      ALLOW_NEW_ACCOUNTS: "true"
      DEBUG: "false"
      DISABLE_ACCOUNTS: "false"
      DISABLE_INTERNAL_ACCOUNTS: "false"
      ALLOW_PASSWORD_CHANGES: "true"
      OIDC_ENABLED: "false"
    networks:
      - my_network

networks:
  my_network:
    external: true
EOF
    
    # Start ByteStash
    echo "Starting ByteStash..."
    cd "$BYTESTASH_DIR"
    
    # Create network if it doesn't exist
    docker network create my_network 2>/dev/null || true
    
    # Pull the latest image and start
    docker compose pull
    docker compose up -d
    
    echo -e "${GREEN}ByteStash installation completed successfully!${NC}"
    echo -e "${CYAN}Access ByteStash at: http://$server_ip:5000${NC}"
    echo -e "${CYAN}Data directory: $BYTESTASH_DIR/data${NC}"
    
    return 0
}

# Remove ByteStash function
remove_bytestash() {
    echo "Removing ByteStash..."
    
    # Stop and remove container
    if check_container_exists "$BYTESTASH_CONTAINER_NAME"; then
        echo "Stopping ByteStash container..."
        cd "$BYTESTASH_DIR" 2>/dev/null || true
        docker compose down 2>/dev/null || docker stop "$BYTESTASH_CONTAINER_NAME" 2>/dev/null || true
        docker rm "$BYTESTASH_CONTAINER_NAME" 2>/dev/null || true
    fi
    
    # Remove data directory (no prompts as requested)
    echo "Removing ByteStash directory and data..."
    sudo rm -rf "$BYTESTASH_DIR"
    echo -e "${GREEN}ByteStash and all data removed successfully.${NC}"
    
    return 0
}

# Backup ByteStash function
backup_bytestash() {
    echo "Starting ByteStash backup..."
    
    # Check if rclone is available
    if ! command -v rclone &> /dev/null; then
        echo "Installing rclone..."
        install_rclone
    fi
    
    # Check if ByteStash directory exists
    if [ ! -d "$BYTESTASH_DIR" ]; then
        echo -e "${RED}ByteStash directory not found at $BYTESTASH_DIR${NC}"
        return 1
    fi
    
    # Create backup
    local timestamp=$(date +"%Y%m%d_%H%M%S")
    local backup_file="bytestash-complete-backup_${timestamp}.tar.gz"
    local temp_backup_dir="/tmp/bytestash_backup_${timestamp}"
    
    echo "Creating backup archive..."
    mkdir -p "$temp_backup_dir"
    
    # Stop container temporarily for consistent backup
    local was_running=false
    if check_container_exists "$BYTESTASH_CONTAINER_NAME" && docker ps --format '{{.Names}}' | grep -q "^$BYTESTASH_CONTAINER_NAME$"; then
        was_running=true
        echo "Stopping ByteStash for backup..."
        cd "$BYTESTASH_DIR"
        docker compose stop 2>/dev/null || docker stop "$BYTESTASH_CONTAINER_NAME" 2>/dev/null || true
    fi
    
    # Create backup archive
    echo "Archiving ByteStash data and configuration..."
    tar -czf "$temp_backup_dir/$backup_file" -C "$(dirname "$BYTESTASH_DIR")" "$(basename "$BYTESTASH_DIR")"
    
    # Restart container if it was running
    if [ "$was_running" = true ]; then
        echo "Restarting ByteStash..."
        cd "$BYTESTASH_DIR"
        docker compose start 2>/dev/null || docker start "$BYTESTASH_CONTAINER_NAME" 2>/dev/null || true
    fi
    
    # Upload to cloud storage (remove previous backups first)
    echo "Uploading backup to cloud storage..."
    rclone mkdir "$BYTESTASH_BACKUP_DIR" 2>/dev/null || true

    # Remove all existing ByteStash backups to keep only one version
    echo "Removing previous backups..."
    rclone delete "$BYTESTASH_BACKUP_DIR" --include "bytestash-complete-backup_*.tar.gz" 2>/dev/null || true

    # Upload the new backup
    rclone move "$temp_backup_dir/$backup_file" "$BYTESTASH_BACKUP_DIR/" --progress --transfers 4 --checkers 8
    
    # Cleanup
    rm -rf "$temp_backup_dir"
    
    echo -e "${GREEN}ByteStash backup completed successfully!${NC}"
    echo -e "${CYAN}Backup saved to: $BYTESTASH_BACKUP_DIR/$backup_file${NC}"
    
    return 0
}

# Restore ByteStash function
restore_bytestash() {
    echo "Starting ByteStash restore..."
    
    # Check if rclone is available
    if ! command -v rclone &> /dev/null; then
        echo "Installing rclone..."
        install_rclone
    fi
    
    # List available backups
    echo "Checking for available backups..."
    local backup_list=$(rclone ls "$BYTESTASH_BACKUP_DIR" 2>/dev/null | grep "bytestash.*backup.*\.tar\.gz" | awk '{print $2}' | sort -r)
    
    if [ -z "$backup_list" ]; then
        echo -e "${RED}No ByteStash backups found in $BYTESTASH_BACKUP_DIR${NC}"
        return 1
    fi
    
    echo "Available backups:"
    echo "$backup_list" | head -5 | nl
    
    # Auto-select the most recent backup
    local selected_backup=$(echo "$backup_list" | head -1)
    echo -e "${CYAN}Auto-selecting most recent backup: $selected_backup${NC}"
    
    # Download backup
    local temp_restore_dir="/tmp/bytestash_restore_$(date +%s)"
    mkdir -p "$temp_restore_dir"
    
    echo "Downloading backup..."
    rclone copy "$BYTESTASH_BACKUP_DIR/$selected_backup" "$temp_restore_dir/" --progress --transfers 4 --checkers 8
    
    # Stop and remove existing installation
    if check_container_exists "$BYTESTASH_CONTAINER_NAME"; then
        echo "Stopping existing ByteStash installation..."
        cd "$BYTESTASH_DIR" 2>/dev/null || true
        docker compose down 2>/dev/null || docker stop "$BYTESTASH_CONTAINER_NAME" 2>/dev/null || true
        docker rm "$BYTESTASH_CONTAINER_NAME" 2>/dev/null || true
    fi
    
    # Remove existing directory
    if [ -d "$BYTESTASH_DIR" ]; then
        echo "Removing existing ByteStash directory..."
        sudo rm -rf "$BYTESTASH_DIR"
    fi
    
    # Extract backup
    echo "Extracting backup..."
    cd "$(dirname "$BYTESTASH_DIR")"
    tar -xzf "$temp_restore_dir/$selected_backup"
    
    # Set proper permissions
    sudo chown -R $USER:$USER "$BYTESTASH_DIR"
    
    # Start ByteStash
    echo "Starting restored ByteStash..."
    cd "$BYTESTASH_DIR"
    
    # Create network if it doesn't exist
    docker network create my_network 2>/dev/null || true
    
    # Start the service
    docker compose up -d
    
    # Cleanup
    rm -rf "$temp_restore_dir"
    
    echo -e "${GREEN}ByteStash restore completed successfully!${NC}"
    echo -e "${CYAN}Access ByteStash at: http://$server_ip:5000${NC}"
    echo -e "${CYAN}Data directory: $BYTESTASH_DIR/data${NC}"
    
    return 0
}

# Check ByteStash status
check_bytestash_status() {
    echo "ByteStash Status"
    echo "==============="
    
    if [ -d "$BYTESTASH_DIR" ]; then
        echo -e "${GREEN}✓ Installation directory exists: $BYTESTASH_DIR${NC}"
        
        if check_container_exists "$BYTESTASH_CONTAINER_NAME"; then
            if docker ps --format '{{.Names}}' | grep -q "^$BYTESTASH_CONTAINER_NAME$"; then
                echo -e "${GREEN}✓ Container is running${NC}"
                echo -e "${CYAN}Access URL: http://$server_ip:5000${NC}"
            else
                echo -e "${YELLOW}⚠ Container exists but is not running${NC}"
            fi
        else
            echo -e "${RED}✗ Container does not exist${NC}"
        fi
        
        # Show data directory info
        if [ -d "$BYTESTASH_DIR/data" ]; then
            local snippet_count=$(find "$BYTESTASH_DIR/data" -name "*.json" 2>/dev/null | wc -l)
            echo -e "${CYAN}Data directory: $BYTESTASH_DIR/data${NC}"
            echo -e "${CYAN}Snippet files: $snippet_count${NC}"
        fi
    else
        echo -e "${RED}✗ ByteStash is not installed${NC}"
    fi
    
    echo ""
    read -rp "Press Enter to continue..."
}
