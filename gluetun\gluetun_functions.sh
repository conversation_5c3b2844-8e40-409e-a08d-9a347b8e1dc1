# Main Gluetun functions file - sources all modular components
# This file serves as the entry point that loads all specialized modules

# Get the directory where this script is located
GLUETUN_MODULE_DIR="$(dirname "${BASH_SOURCE[0]}")"

# Source all required modules
source "$GLUETUN_MODULE_DIR/gluetun_core.sh"
source "$GLUETUN_MODULE_DIR/gluetun_install_pia.sh"
source "$GLUETUN_MODULE_DIR/gluetun_install_fastestvpn.sh"
source "$GLUETUN_MODULE_DIR/gluetun_install_custom.sh"
source "$GLUETUN_MODULE_DIR/gluetun_management.sh"

# Module Overview:
# ================
# - gluetun_core.sh: Core utilities and shared functions
# - gluetun_install_pia.sh: PIA VPN installation functionality
# - gluetun_install_fastestvpn.sh: FastestVPN installation functionality
# - gluetun_install_custom.sh: Custom VPN installation functionality
# - gluetun_management.sh: Container management and removal functions
# - gluetun_menu.sh: Menu interfaces and UI functions
# - pia_config.sh: PIA-specific configuration and WireGuard generation

# All functions are now available through their respective modules
# This modular approach keeps file sizes manageable (200-300 lines each)
# while maintaining all existing functionality and backward compatibility






