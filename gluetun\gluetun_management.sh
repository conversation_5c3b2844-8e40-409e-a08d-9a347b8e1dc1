#!/bin/bash

# Gluetun Management Module
# This module handles container management and removal functions

# Remove Gluetun container and its dependencies
remove_gluetun() {
    local container_to_remove="$1" # Capture the optional container name argument
    
    # Find all containers using the gluetun image (any version)
    local gluetun_containers
    gluetun_containers=$(docker ps -a --format '{{.Names}}' | while read container; do
        if docker inspect "$container" | grep -q '"Image": "qmcgaw/gluetun'; then
            echo "$container"
        fi
    done)

    if [ -z "$gluetun_containers" ]; then
        echo "No Gluetun containers found."
        return 0
    fi

    local selected_container
    # Check if a specific container name was provided as an argument and if it exists
    if [ -n "$container_to_remove" ] && echo "$gluetun_containers" | grep -q "^${container_to_remove}$"; then
        selected_container="$container_to_remove"
        echo "Removing specified Gluetun container: '$selected_container'"
    else
        # If no specific container was provided or found, proceed with interactive selection
        selected_container=$(select_gluetun_container "$gluetun_containers")
        if [ -z "$selected_container" ]; then
            return 1
        fi
    fi

    # Get selected container ID
    local gluetun_id
    gluetun_id=$(docker inspect --format '{{.Id}}' "$selected_container")

    # Find and handle dependent containers
    if ! handle_dependent_containers "$selected_container" "$gluetun_id"; then
        return 1
    fi

    # Remove the selected gluetun container
    echo "Removing Gluetun container '$selected_container'..."
    docker rm -f "$selected_container"

    echo "Gluetun and all dependent containers have been removed successfully."
    return 0
}

# Select a Gluetun container from the list
select_gluetun_container() {
    local gluetun_containers="$1"
    
    # If there's only one container, use it directly
    if [ "$(echo "$gluetun_containers" | wc -l)" -eq 1 ]; then
        echo "$gluetun_containers"
        return 0
    fi

    # Show numbered list of containers
    echo "Found multiple Gluetun containers:"
    local i=1
    while IFS= read -r container; do
        echo "$i) $container"
        i=$((i+1))
    done <<< "$gluetun_containers"

    # Ask user to select a container
    while true; do
        read -p "Enter the number of the container to remove (1-$((i-1))): " selection
        if [[ "$selection" =~ ^[0-9]+$ ]] && [ "$selection" -ge 1 ] && [ "$selection" -lt "$i" ]; then
            echo "$gluetun_containers" | sed -n "${selection}p"
            return 0
        else
            echo "Invalid selection. Please try again."
        fi
    done
}

# Handle containers that depend on the Gluetun container's network
handle_dependent_containers() {
    local selected_container="$1"
    local gluetun_id="$2"
    
    # Find containers using selected gluetun's network
    local dependent_containers
    dependent_containers=$(docker ps -a --format '{{.Names}}' | while read container; do
        if docker inspect "$container" | grep -q "NetworkMode.*container:.*$gluetun_id"; then
            echo "$container"
        fi
    done)

    if [ -n "$dependent_containers" ]; then
        echo -e "\nWARNING: The following containers are using $selected_container's network:"
        echo "$dependent_containers"
        echo
        
        while true; do
            read -p "Do you want to remove these containers as well? (y/n): " yn
            case $yn in
                [Yy]*)
                    echo "Removing dependent containers..."
                    for container in $dependent_containers; do
                        if [ "$container" != "$selected_container" ]; then
                            echo "Removing container: $container"
                            docker rm -f "$container"
                        fi
                    done
                    return 0
                    ;;
                [Nn]*)
                    echo "Cannot remove Gluetun while other containers are using its network."
                    echo "Please remove the dependent containers first."
                    return 1
                    ;;
                *)
                    echo "Please answer y or n."
                    ;;
            esac
        done
    fi
    
    return 0
}

# Get status of all Gluetun containers
get_gluetun_status() {
    echo "Checking Gluetun container status..."
    
    # Find all Gluetun containers
    local gluetun_containers
    gluetun_containers=$(docker ps -a --format 'table {{.Names}}\t{{.Status}}\t{{.Ports}}' --filter ancestor=qmcgaw/gluetun)
    
    if [ -z "$gluetun_containers" ]; then
        echo "No Gluetun containers found."
        return 0
    fi
    
    echo "$gluetun_containers"
    return 0
}

# Check if a specific Gluetun container is running
is_gluetun_running() {
    local container_name="$1"
    
    if [ -z "$container_name" ]; then
        echo "Error: Container name is required"
        return 1
    fi
    
    if docker ps --format '{{.Names}}' | grep -q "^${container_name}$"; then
        return 0
    else
        return 1
    fi
}

# Start a stopped Gluetun container
start_gluetun() {
    local container_name="$1"
    
    if [ -z "$container_name" ]; then
        echo "Error: Container name is required"
        return 1
    fi
    
    if is_gluetun_running "$container_name"; then
        echo "Container '$container_name' is already running."
        return 0
    fi
    
    echo "Starting Gluetun container '$container_name'..."
    if docker start "$container_name"; then
        echo "Container '$container_name' started successfully."
        return 0
    else
        echo "Failed to start container '$container_name'."
        return 1
    fi
}

# Stop a running Gluetun container
stop_gluetun() {
    local container_name="$1"
    
    if [ -z "$container_name" ]; then
        echo "Error: Container name is required"
        return 1
    fi
    
    if ! is_gluetun_running "$container_name"; then
        echo "Container '$container_name' is not running."
        return 0
    fi
    
    echo "Stopping Gluetun container '$container_name'..."
    if docker stop "$container_name"; then
        echo "Container '$container_name' stopped successfully."
        return 0
    else
        echo "Failed to stop container '$container_name'."
        return 1
    fi
}

# Restart a Gluetun container
restart_gluetun() {
    local container_name="$1"
    
    if [ -z "$container_name" ]; then
        echo "Error: Container name is required"
        return 1
    fi
    
    echo "Restarting Gluetun container '$container_name'..."
    if docker restart "$container_name"; then
        echo "Container '$container_name' restarted successfully."
        return 0
    else
        echo "Failed to restart container '$container_name'."
        return 1
    fi
}
