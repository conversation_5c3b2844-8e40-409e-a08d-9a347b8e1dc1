#!/bin/bash

# Netbird configuration backup functions

# Function to test backup components
test_backup_components() {
    echo "Testing Netbird Backup Components"
    echo "================================="
    
    # Test rclone installation
    echo -n "Rclone installation: "
    if install_rclone; then
        echo -e "${GRE<PERSON>}✓ Available${NC}"
    else
        echo -e "${RED}✗ Not available${NC}"
    fi
    
    # Test compression tools
    echo -n "Compression tools: "
    if check_compression_tools; then
        echo -e "${GREEN}✓ Available${NC}"
    else
        echo -e "${RED}✗ Not available${NC}"
    fi
    
    # Test cloud storage connectivity
    echo -n "Cloud storage connectivity: "
    if rclone lsd "$RCLONE_REMOTE:" >/dev/null 2>&1; then
        echo -e "${GREEN}✓ Connected${NC}"
    else
        echo -e "${RED}✗ Not connected${NC}"
    fi
    
    # Test Netbird installation detection
    echo -n "Netbird installation: "
    local netbird_path=$(detect_netbird_installation)
    if [ -n "$netbird_path" ]; then
        echo -e "${GREEN}✓ Found at $netbird_path${NC}"
    else
        echo -e "${RED}✗ Not found${NC}"
    fi
    
    # Test domain detection
    echo -n "Domain detection: "
    local domain=$(detect_netbird_domain)
    if [ -n "$domain" ]; then
        echo -e "${GREEN}✓ Detected: $domain${NC}"
    else
        echo -e "${YELLOW}⚠ Not detected (will use 'unknown-domain')${NC}"
    fi
    
    # Test backup directory creation
    echo -n "Backup directory: "
    local backup_dir="/opt/netbird_backups"
    if mkdir -p "$backup_dir" 2>/dev/null; then
        echo -e "${GREEN}✓ Accessible${NC}"
    else
        echo -e "${RED}✗ Cannot create${NC}"
    fi
    
    echo ""
    read -rp "Press Enter to continue..."
}

# Function to backup Netbird configuration files
backup_netbird_config() {
    local netbird_path=$(detect_netbird_installation)

    if [ -z "$netbird_path" ]; then
        echo "Netbird installation not found."
        return 1
    fi

    # Detect domain name for organized backup
    local domain=$(detect_netbird_domain)
    if [ -z "$domain" ]; then
        echo "Warning: Could not detect domain name. Using 'unknown-domain' as fallback."
        domain="unknown-domain"
    fi

    echo "Detected domain: $domain"

    local backup_dir="/opt/netbird_backups"
    local timestamp=$(date +"%Y%m%d_%H%M%S")
    local config_backup_file="netbird_config_backup_${timestamp}.tar.gz"
    local config_backup_path="$backup_dir/$config_backup_file"
    local rclone_dest="$RCLONE_REMOTE:/Backups/Netbird/$domain"
    
    echo "Backing up Netbird configuration files..."
    
    # Check if rclone is installed
    if ! install_rclone; then
        echo "Failed to verify/install Rclone. Backup cannot proceed."
        return 1
    fi
    
    # Create backup directory
    mkdir -p "$backup_dir"
    
    # Check compression tools
    if ! check_compression_tools; then
        echo "Required compression tools are not available."
        return 1
    fi
    
    # Create list of config files to backup
    local config_files=()
    cd "$netbird_path"
    
    # Common configuration files
    [ -f "docker-compose.yml" ] && config_files+=("docker-compose.yml")
    [ -f "management.json" ] && config_files+=("management.json")
    [ -f "turnserver.conf" ] && config_files+=("turnserver.conf")
    [ -f "setup.env" ] && config_files+=("setup.env")
    [ -f "Caddyfile" ] && config_files+=("Caddyfile")
    [ -f "zitadel.env" ] && config_files+=("zitadel.env")
    [ -f "dashboard.env" ] && config_files+=("dashboard.env")
    [ -f "relay.env" ] && config_files+=("relay.env")
    [ -f "zdb.env" ] && config_files+=("zdb.env")
    
    if [ ${#config_files[@]} -eq 0 ]; then
        echo "No configuration files found to backup."
        return 1
    fi
    
    # Create tar archive of config files
    echo "Creating configuration backup archive..."
    if tar -czf "$config_backup_path" "${config_files[@]}" 2>/dev/null; then
        echo "Configuration backup created successfully."
        
        # Upload to cloud storage (overwrite existing backup for this domain)
        echo "Uploading configuration backup to cloud storage..."
        echo "Destination: $rclone_dest"

        # Remove existing config backup for this domain first
        echo "Removing any existing configuration backup for domain: $domain"
        rclone delete "$rclone_dest" --include "netbird_config_backup_*.tar.gz" 2>/dev/null || true

        if rclone copy "$config_backup_path" "$rclone_dest" --progress; then
            echo "Configuration backup uploaded successfully to domain folder: $domain"
            rm -f "$config_backup_path"
            return 0
        else
            echo "Failed to upload configuration backup."
            rm -f "$config_backup_path"
            return 1
        fi
    else
        echo "Failed to create configuration backup."
        return 1
    fi
}

# Function to list domain backups
list_domain_backups() {
    echo "Available Domain Backups"
    echo "======================="

    local rclone_source="$RCLONE_REMOTE:/Backups/Netbird"

    # Check if rclone is installed
    if ! install_rclone; then
        echo "Rclone is not available. Cannot list domain backups."
        return 1
    fi

    echo "Listing domain-based backups from cloud storage..."
    echo ""

    # Get list of domains
    local domains=$(rclone lsd "$rclone_source" 2>/dev/null | awk '{print $5}' | grep -v "^$")

    if [ -n "$domains" ]; then
        echo "Available domains with backups:"
        echo "==============================="

        local domain_count=0
        while IFS= read -r domain; do
            if [ -n "$domain" ]; then
                ((domain_count++))
                echo ""
                echo "$domain_count. Domain: $domain"
                echo "$(printf '%.0s-' $(seq 1 $((${#domain} + 10))))"
                local domain_path="$rclone_source/$domain"

                # Check what backup types are available
                local config_backup=$(rclone ls "$domain_path" 2>/dev/null | grep "netbird_config_backup_" | wc -l)
                local data_backup=$(rclone ls "$domain_path" 2>/dev/null | grep "netbird_data_backup_" | wc -l)
                local volumes_backup=$(rclone ls "$domain_path" 2>/dev/null | grep "netbird_volumes_backup_" | wc -l)

                echo "   Available backups:"
                [ $config_backup -gt 0 ] && echo "   ✓ Configuration backup"
                [ $data_backup -gt 0 ] && echo "   ✓ Data backup"
                [ $volumes_backup -gt 0 ] && echo "   ✓ Volumes backup"

                if [ $config_backup -eq 0 ] && [ $data_backup -eq 0 ] && [ $volumes_backup -eq 0 ]; then
                    echo "   ✗ No backup files found"
                fi

                # Show file details
                echo "   Files:"
                if rclone lsl "$domain_path" 2>/dev/null | grep -q "netbird.*backup"; then
                    rclone lsl "$domain_path" 2>/dev/null | grep "netbird.*backup" | while read -r line; do
                        echo "     $line"
                    done
                else
                    echo "     No backup files found"
                fi
            fi
        done <<< "$domains"

        if [ $domain_count -eq 0 ]; then
            echo "No domains found with backups."
        fi
    else
        echo "No domain-based backups found in cloud storage."
        echo ""
        echo "This could mean:"
        echo "- No backups have been created yet"
        echo "- Rclone configuration issue"
        echo "- Network connectivity problem"
    fi

    echo ""
    read -rp "Press Enter to continue..."
}

# Function to list available Netbird backups
list_netbird_backups() {
    echo "Available Netbird Backups"
    echo "========================="

    local rclone_source="$RCLONE_REMOTE:/Backups/Netbird"

    # Check if rclone is installed
    if ! install_rclone; then
        echo "Rclone is not available. Cannot list backups."
        return 1
    fi

    echo "Listing backups from cloud storage..."
    echo ""

    # Check for domain-based backups
    local domains=$(rclone lsd "$rclone_source" 2>/dev/null | awk '{print $5}' | grep -v "^$")

    if [ -n "$domains" ]; then
        echo "Domain-based backups:"
        echo "===================="

        while IFS= read -r domain; do
            if [ -n "$domain" ]; then
                echo ""
                echo "Domain: $domain"
                echo "$(printf '%.0s-' $(seq 1 ${#domain}))"
                local domain_path="$rclone_source/$domain"

                if rclone lsl "$domain_path" 2>/dev/null; then
                    echo ""
                else
                    echo "  No files found or inaccessible"
                fi
            fi
        done <<< "$domains"

        echo ""
        echo "To restore from a specific domain, use option 6 in the restore menu."
    fi

    # Check for legacy backups
    local legacy_files=$(rclone ls "$rclone_source" 2>/dev/null | grep "netbird.*backup")
    if [ -n "$legacy_files" ]; then
        echo ""
        echo "Legacy backups (not organized by domain):"
        echo "=========================================="
        rclone lsl "$rclone_source" 2>/dev/null | grep "netbird.*backup"
        echo ""
        echo "Legacy backups can be restored using options 1-4 in the restore menu."
    fi

    if [ -z "$domains" ] && [ -z "$legacy_files" ]; then
        echo "No backups found or unable to access cloud storage."
        echo "Please check your rclone configuration."
    fi

    echo ""
    read -rp "Press Enter to continue..."
}
