#!/bin/bash

# Netbird menu utility functions
# This file contains utility functions used by the menu system

# Function to check installation status for menu display
get_installation_status() {
    local netbird_path=$(detect_netbird_installation)
    if [ -n "$netbird_path" ]; then
        local domain=$(detect_netbird_domain)
        local domain_info=""
        if [ -n "$domain" ]; then
            domain_info=" | Domain: $domain"
        fi

        if is_netbird_running; then
            echo "✓ Installed & Running$domain_info"
        else
            echo "✓ Installed (Stopped)$domain_info"
        fi
    else
        echo "✗ Not Installed"
    fi
}

# Function to view backup status
view_backup_status() {
    echo "Netbird Backup Status"
    echo "===================="

    local rclone_source="$RCLONE_REMOTE:/Backups/Netbird"

    # Check if rclone is installed
    if ! install_rclone; then
        echo "Rclone is not available. Cannot check backup status."
        return 1
    fi

    echo "Checking cloud storage for backups..."

    # Check for domain-based backups first
    local domains=$(rclone lsd "$rclone_source" 2>/dev/null | awk '{print $5}' | grep -v "^$")

    if [ -n "$domains" ]; then
        echo ""
        echo "Domain-based backups found:"
        echo "============================"

        while IFS= read -r domain; do
            if [ -n "$domain" ]; then
                echo ""
                echo "Domain: $domain"
                echo "---------------"
                local domain_path="$rclone_source/$domain"
                local files=$(rclone ls "$domain_path" 2>/dev/null)

                if [ -n "$files" ]; then
                    echo "$files" | while read -r size file; do
                        local size_human=$(numfmt --to=iec $size 2>/dev/null || echo "$size bytes")
                        echo "  - $file ($size_human)"
                    done
                else
                    echo "  - No files found"
                fi
            fi
        done <<< "$domains"
    fi

    # Check for legacy backups (files directly in Netbird folder)
    local legacy_files=$(rclone ls "$rclone_source" 2>/dev/null | grep "netbird.*backup")
    if [ -n "$legacy_files" ]; then
        echo ""
        echo "Legacy backups (not organized by domain):"
        echo "=========================================="
        echo "$legacy_files" | while read -r size file; do
            local size_human=$(numfmt --to=iec $size 2>/dev/null || echo "$size bytes")
            echo "  - $file ($size_human)"
        done
    fi

    if [ -z "$domains" ] && [ -z "$legacy_files" ]; then
        echo "No Netbird backups found in cloud storage."
    fi
    
    # Check local backup directory
    local backup_dir="/opt/netbird_backups"
    if [ -d "$backup_dir" ] && [ "$(ls -A "$backup_dir" 2>/dev/null)" ]; then
        echo ""
        echo "Local backup files:"
        echo "------------------"
        ls -la "$backup_dir"
    else
        echo ""
        echo "No local backup files found."
    fi
    echo ""
    read -rp "Press Enter to continue..."
}

# Function to show Netbird installation info
show_netbird_info() {
    echo "Netbird Installation Information"
    echo "==============================="

    local netbird_path=$(detect_netbird_installation)

    if [ -n "$netbird_path" ]; then
        echo "✓ Installation found at: $netbird_path"
        echo ""

        # Show configuration files
        echo "Configuration files:"
        cd "$netbird_path"
        for file in docker-compose.yml management.json turnserver.conf Caddyfile zitadel.env dashboard.env; do
            if [ -f "$file" ]; then
                echo "  ✓ $file"
            else
                echo "  ✗ $file (missing)"
            fi
        done

        echo ""
        echo "Docker containers:"
        docker ps -a --format "table {{.Names}}\t{{.Image}}\t{{.Status}}" | grep -E "management|signal|dashboard|coturn|relay|zitadel|caddy" || echo "No Netbird containers found"

        # Show credentials if available
        if [ -f "$netbird_path/.env" ]; then
            echo ""
            echo "Login credentials:"
            cat "$netbird_path/.env"
        fi

        # Show domain info
        if [ -f "$netbird_path/zitadel.env" ]; then
            local domain=$(grep "ZITADEL_EXTERNALDOMAIN" "$netbird_path/zitadel.env" 2>/dev/null | cut -d'=' -f2)
            if [ -n "$domain" ]; then
                echo ""
                echo "Domain: $domain"
                echo "Access URL: https://$domain"
            fi
        fi

    else
        echo "✗ Netbird installation not detected."
        echo ""
        echo "To install Netbird:"
        echo "  1. Select 'Install Netbird' from the menu"
        echo "  2. Provide your domain name"
        echo "  3. Wait for automatic installation"
        echo ""
        echo "Default installation location: /opt/netbird"
    fi
    echo ""
    read -rp "Press Enter to continue..."
}
