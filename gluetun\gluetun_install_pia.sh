#!/bin/bash

# Gluetun PIA Installation Module
# This module handles Private Internet Access (PIA) VPN installation functionality

# Install Gluetun with PIA configuration
install_gluetun_pia() {
    local preset_container_name="$1"
    local preset_port="$2"

    install_docker

    # Check for existing containers only if not called with preset parameters
    if [ -z "$preset_container_name" ]; then
        if ! check_existing_containers; then
            return 0
        fi
    fi

    # Clean up any existing PIA config files before generating new one
    if [ -d "/root/pia-wg" ]; then
        echo "Cleaning up old PIA config files..."
        find /root/pia-wg -type f -name "PIA-*.conf" -delete
    fi
    
    # Generate PIA WireGuard file
    generate_pia_wireguard_file
    
    # Get the newly generated config file
    conf_file=$(find /root/pia-wg -maxdepth 1 -name "PIA-*.conf" -type f -printf '%T@ %p\n' | sort -n | tail -1 | cut -f2- -d" ")

    if [ -n "$conf_file" ]; then
        echo "Using configuration file: ${conf_file}"
        
        # Parse PIA configuration file
        if ! parse_pia_config "$conf_file"; then
            return 1
        fi

        # Get container name (use preset if provided, otherwise ask user)
        local container_name
        if [ -n "$preset_container_name" ]; then
            container_name="$preset_container_name"
        else
            local default_name=$(get_next_gluetun_number)
            if ! container_name=$(get_container_name "$default_name"); then
                return 1
            fi
        fi

        # Get port configuration (use preset if provided, otherwise ask user)
        local ports
        local port_mappings
        if [ -n "$preset_port" ]; then
            ports="$preset_port"
            port_mappings="-p ${preset_port}:${preset_port}/tcp -p ${preset_port}:${preset_port}/udp"
        else
            get_port_configuration "$container_name"
            ports="$GLUETUN_PORTS"
            port_mappings="$GLUETUN_PORT_MAPPINGS"
        fi

        # Create and start the Gluetun container with PIA configuration
        if create_pia_container "$container_name" "$port_mappings" "$ports"; then
            check_container_status "$container_name" "$ports" "$DNS"
        else
            echo "Error: Container failed to start"
            return 1
        fi
    else
        echo "No PIA WireGuard configuration file found in /root/pia-wg/"
        return 1
    fi
}

# Parse PIA configuration file
parse_pia_config() {
    local conf_file="$1"
    
    # Initialize variables
    ADDRESS=""
    PRIVATE_KEY=""
    DNS=""
    WIREGUARD_PUBLIC_KEY=""
    ENDPOINT_IP=""
    ENDPOINT_PORT=""

    while IFS= read -r line; do
        if [[ $line == "Address ="* ]]; then
            ADDRESS=$(echo $line | cut -d' ' -f3)
        elif [[ $line == "PrivateKey ="* ]]; then
            PRIVATE_KEY=$(echo $line | cut -d' ' -f3)
        elif [[ $line == "DNS ="* ]]; then
            DNS=$(echo $line | cut -d' ' -f3)
            # Fallback to common DNS servers if PIA DNS is not available
            if [ -z "$DNS" ] || [ "$DNS" = "**********" ]; then
                # Use Cloudflare's DNS as fallback
                DNS="*******"
                echo "Warning: Using Cloudflare DNS (*******) as fallback since PIA DNS was not available"
            fi
        elif [[ $line == "PublicKey ="* ]]; then
            WIREGUARD_PUBLIC_KEY=$(echo $line | cut -d' ' -f3)
        elif [[ $line == "Endpoint ="* ]]; then
            ENDPOINT_IP=$(echo $line | cut -d' ' -f3 | cut -d':' -f1)
            ENDPOINT_PORT=$(echo $line | cut -d' ' -f3 | cut -d':' -f2)
        fi
    done < "$conf_file"

    # Validate that all required variables are set
    if [ -z "$ADDRESS" ] || [ -z "$PRIVATE_KEY" ] || [ -z "$DNS" ] || \
       [ -z "$WIREGUARD_PUBLIC_KEY" ] || [ -z "$ENDPOINT_IP" ] || [ -z "$ENDPOINT_PORT" ]; then
        echo "Error: WireGuard configuration file is missing required fields"
        echo "Please ensure the configuration file contains all necessary fields:"
        echo "- Address"
        echo "- PrivateKey"
        echo "- DNS"
        echo "- PublicKey"
        echo "- Endpoint"
        return 1
    fi
    
    return 0
}

# Create PIA Gluetun container
create_pia_container() {
    local container_name="$1"
    local port_mappings="$2"
    local ports="$3"
    
    # Get common Docker parameters and environment variables
    local common_params=$(get_common_docker_params)
    local common_env=$(get_common_env_vars "$ports")

    # Create and start the container
    docker run -d \
        --name="${container_name}" \
        $common_params \
        -e VPN_SERVICE_PROVIDER=custom \
        -e VPN_TYPE=wireguard \
        -e WIREGUARD_ENDPOINT_IP="${ENDPOINT_IP}" \
        -e WIREGUARD_ENDPOINT_PORT="${ENDPOINT_PORT}" \
        -e WIREGUARD_PUBLIC_KEY="${WIREGUARD_PUBLIC_KEY}" \
        -e WIREGUARD_PRIVATE_KEY="${PRIVATE_KEY}" \
        -e WIREGUARD_ADDRESSES="${ADDRESS}/32" \
        -e DNS="${DNS}" \
        $common_env \
        ${port_mappings} \
        $DOCKER_NETWORK \
        qmcgaw/gluetun:latest

    return $?
}
